import {
  Component,
  HostL<PERSON>ener,
  On<PERSON>nit,
  <PERSON><PERSON><PERSON>roy,
  ChangeDetectorRef,
  // Renderer2 is imported but currently unused
  // Renderer2,
  Input,
  NgZone,
  ViewChild,
  ElementRef,
  AfterViewInit,
  ChangeDetectionStrategy,
} from '@angular/core';
import { Subscription, Subject, BehaviorSubject, Observable } from 'rxjs';
import { takeUntil, take } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { createLogger } from '../../utils/logger';
import { UIDesignCanvasService } from './services/ui-design-canvas.service';
import { UIDesignNodeService, UIDesignNode, UIDesignPage } from './services/ui-design-node.service';
import { UIDesignViewportService } from './services/ui-design-viewport.service';
import { SafeSrcdocDirective } from '../../directives/safe-srcdoc.directive';
import {
  IconsComponent,
  LeftPanelComponent,
  RightPanelComponent,
  SplitScreenComponent,
} from '@awe/play-comp-library';
import { FileModel } from '../code-viewer/code-viewer.component';
import { SafeResourceUrl } from '@angular/platform-browser';
import { CodeGenerationService } from '../../services/code-generation.service';
import { CodeSharingService } from '../../services/code-sharing.service';
import { PollingService } from '../../services/polling.service';
import { ThemeService } from '../../services/theme-service/theme.service';
import { AppStateService } from '../../services/app-state.service';
import { PromptBarService } from '../../services/prompt-bar-servises/prompt-bar.service';
import { PromptSubmissionService } from '../../services/prompt-submission.service';
import { UserSignatureService } from '../../services/user-signature.service';
import { TextTransformationService } from '../../services/text-transformation.service';
import { StepperStateService } from '../../services/stepper-state.service';
import { ToastService } from '../../services/toast.service';
import { NewPollingResponseProcessorService } from '../../services/new-polling-response-processor.service';
// import { VSCodeExportService, VSCodeExportResult } from '../../services/vscode-export/vscode-export.service';
import { VSCodeExportService,VSCodeExportResult } from '../../services/vscode-export/vscode-export.service';
import { FileTreePersistenceService } from '../../services/file-tree-persistence.service';
import { MonacoStateManagementService } from '../../services/monaco-state-management.service';
import { GenerateUIDesignService, UIDesignResponseData } from '../../services/generate-ui-design.service';
import { UIDesignSelectionService, MultiSelectedNodeData } from '../../services/ui-design-selection.service';
import { UIDesignEditService } from '../../services/ui-design-edit.service';
import { UIDesignNodePositioningService } from './services/ui-design-node-positioning.service';
import { UIDesignVisualFeedbackService } from '../../services/ui-design-visual-feedback.service';
import { WireframeGenerationStateService } from '../../services/wireframe-generation-state.service';
import { FilenameNormalizationService } from '../../services/filename-normalization.service';
import { UIDesignFilenameTransformerService } from '../../services/ui-design-filename-transformer.service';
import { WireframeNodeManagementService } from '../../services/wireframe-node-management.service';
import { UIDesignIntroService, IntroMessageState } from '../../services/ui-design-intro.service';

import { CodeViewerComponent } from '../code-viewer/code-viewer.component';
import { LoadingAnimationComponent } from './loading-animation/loading-animation.component';
import { LayoutIdentifiedAnimationComponent } from './layout-animation/layout-identified-animation.component';
import { MarkdownModule } from 'ngx-markdown';
import { ChatWindowComponent } from '../chat-window/chat-window.component';
import { ErrorPageComponent } from '../error-page/error-page.component';
import { CanvasInfoComponent } from './components/canvas-info/canvas-info.component';
import { MobileFrameComponent, MobilePage } from '../mobile-frame/mobile-frame.component';
import { StepperState } from '../../models/stepper-states.enum';
import {
  ProgressState,
  StatusType,
  ArtifactData,
  FileData,
  DesignTokensData,
  ProjectInfo
} from '../../models/polling-response.interface';
// Azure URL generation removed - using Netlify URLs from deployment response

import JSZip from 'jszip';
import {
  ALL_DESIGN_TOKENS,
  isValidHexColor as validateHexColor
} from '../../data/design-tokens.data';

type IconStatus = 'default' | 'active' | 'disable';

// Interface for UI Design API response
export interface UIDesignAPIResponse {
  pageName: string;
  content: string;
}

// Interface for Wireframe API response
export interface WireframeAPIResponse {
  fileName: string;
  content: string;
}

// Interface for UI Design page data
export interface UIDesignPageData {
  fileName: string;
  content: string;
}

/**
 * Interface for design tokens that can be edited
 */
interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
}

/**
 * Interface for font style tokens
 */
interface FontStyleToken {
  name: string;
  size: string;
  weight: string;
  lineHeight: string;
}

/**
 * Interface for color tokens
 */
interface ColorToken {
  name: string;
  value: string;
  hexCode: string;
}

/**
 * Interface for button tokens
 */
interface ButtonToken {
  label: string;
  variant: string;
  hasIcon?: boolean;
}

// Azure website generation removed - using Netlify URLs from deployment response
@Component({
  selector: 'app-code-window',
  standalone: true,
  imports: [
    CommonModule,
    SplitScreenComponent,
    LeftPanelComponent,
    RightPanelComponent,
    IconsComponent,
    CodeViewerComponent,
    LoadingAnimationComponent,
    LayoutIdentifiedAnimationComponent,
    ChatWindowComponent,
    ErrorPageComponent,
    MarkdownModule,
    CanvasInfoComponent,
    MobileFrameComponent,
    SafeSrcdocDirective,
  ],
  templateUrl: './code-window.component.html',
  styleUrls: ['./code-window.component.scss', './artifacts-view.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CodeWindowComponent implements OnInit, AfterViewInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private logger = createLogger('CodeWindow');

  // Convert properties to observables for OnPush optimization
  private files$ = new BehaviorSubject<FileModel[]>([]);
  private isResizing$ = new BehaviorSubject<boolean>(false);
  private currentView$ = new BehaviorSubject<'loading' | 'editor' | 'preview' | 'overview' | 'logs' | 'artifacts'>('preview');
  private isPreviewLoading$ = new BehaviorSubject<boolean>(false);
  private previewIcon$ = new BehaviorSubject<string>('bi-code-slash');
  private previewError$ = new BehaviorSubject<boolean>(false);
  private errorDescription$ = new BehaviorSubject<string>('Please try again later.');
  private errorTerminalOutput$ = new BehaviorSubject<string>('');
  private currentTheme$ = new BehaviorSubject<'light' | 'dark'>('light');

  // ENHANCED: Regeneration state management
  private isRegenerationInProgress$ = new BehaviorSubject<boolean>(false);
  private regenerationStartTime: number = 0;
  private deployedUrl$ = new BehaviorSubject<string | null>('');
  private isPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private isLoading$ = new BehaviorSubject<boolean>(true);
  private isHistoryActive$ = new BehaviorSubject<boolean>(false);
  private isCodeActive$ = new BehaviorSubject<boolean>(false);
  private isPreviewActive$ = new BehaviorSubject<boolean>(true);
  private isLogsActive$ = new BehaviorSubject<boolean>(false);
  private isArtifactsActive$ = new BehaviorSubject<boolean>(false);
  private isLeftPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private minWidth$ = new BehaviorSubject<string>('300px');
  private isExperienceStudioModalOpen$ = new BehaviorSubject<boolean>(false);
  private checkSessionStorageInterval: any;
  private shouldHideProjectName$ = new BehaviorSubject<boolean>(false);

  // UI Design mode properties
  private isUIDesignMode$ = new BehaviorSubject<boolean>(false);
  private uiDesignNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private selectedUIDesignNode$ = new BehaviorSubject<UIDesignNode | null>(null);
  private isUIDesignFullScreenOpen$ = new BehaviorSubject<boolean>(false);
  private uiDesignViewMode$ = new BehaviorSubject<'mobile' | 'web'>('mobile');
  private isUIDesignModalFullScreen$ = new BehaviorSubject<boolean>(false);

  // UI Design workflow state management
  private isUIDesignGenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignError$ = new BehaviorSubject<string | null>(null);
  private uiDesignApiInProgress$ = new BehaviorSubject<boolean>(false);
  private isWireframeGenerationComplete$ = new BehaviorSubject<boolean>(false);

  // UI Design regeneration state management
  private isUIDesignRegenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignLoadingNodes$ = new BehaviorSubject<UIDesignNode[]>([]);

  // UI Design selection and editing state
  private showCanvasTooltip$ = new BehaviorSubject<boolean>(true);
  private isEditingUIDesign$ = new BehaviorSubject<boolean>(false);

  // UI Design intro message state
  private introMessageState$ = new BehaviorSubject<IntroMessageState>({
    isLoading: false,
    text: '',
    isTyping: false,
    hasError: false,
    shouldReplaceText: false,
    introAPICompleted: false,
    mainAPIInProgress: false,
    showLoadingIndicator: false,
    loadingPhase: 'intro',
    messageType: 'generation'
  });

  // Enhanced AI message tracking for duplicate prevention and persistence
  private activeAIMessageIds = new Set<string>();
  private currentActiveMessageId: string | null = null;

  // Regeneration session tracking to ensure separate messages for each regeneration
  private regenerationSessionCounter = 0;
  private activeRegenerationSessions = new Map<string, {
    sessionId: string;
    messageId: string;
    timestamp: number;
    prompt: string;
    selectedNodes: MultiSelectedNodeData[];
  }>();

  // UI Design overview properties
  private showUIDesignOverviewTab$ = new BehaviorSubject<boolean>(false);
  private uiDesignPages$ = new BehaviorSubject<MobilePage[]>([]);
  private currentUIDesignPageIndex$ = new BehaviorSubject<number>(0);

  // UI Design loading state for clean loading indicator
  private isUIDesignLoading$ = new BehaviorSubject<boolean>(false);

  // Public observable for template consumption
  readonly shouldHideProjectName = this.shouldHideProjectName$.asObservable();
  readonly isUIDesignMode = this.isUIDesignMode$.asObservable();
  readonly uiDesignNodes = this.uiDesignNodes$.asObservable();
  readonly selectedUIDesignNode = this.selectedUIDesignNode$.asObservable();
  readonly isUIDesignFullScreenOpen = this.isUIDesignFullScreenOpen$.asObservable();
  readonly uiDesignViewMode = this.uiDesignViewMode$.asObservable();
  readonly isUIDesignModalFullScreen = this.isUIDesignModalFullScreen$.asObservable();

  // UI Design workflow state observables
  readonly isUIDesignGenerating = this.isUIDesignGenerating$.asObservable();
  readonly uiDesignError = this.uiDesignError$.asObservable();
  readonly uiDesignApiInProgress = this.uiDesignApiInProgress$.asObservable();
  readonly isWireframeGenerationComplete = this.isWireframeGenerationComplete$.asObservable();
  readonly isUIDesignRegenerating = this.isUIDesignRegenerating$.asObservable();
  readonly uiDesignLoadingNodes = this.uiDesignLoadingNodes$.asObservable();
  readonly isUIDesignLoading = this.isUIDesignLoading$.asObservable();

  // UI Design selection and editing observables
  readonly showCanvasTooltip = this.showCanvasTooltip$.asObservable();
  readonly isEditingUIDesign = this.isEditingUIDesign$.asObservable();

  // UI Design intro message observable
  readonly introMessageState = this.introMessageState$.asObservable();

  // UI Design overview observables
  readonly showUIDesignOverviewTab = this.showUIDesignOverviewTab$.asObservable();
  readonly uiDesignPages = this.uiDesignPages$.asObservable();
  readonly currentUIDesignPageIndex = this.currentUIDesignPageIndex$.asObservable();

  // Flag to track if user has manually selected a tab
  private userSelectedTab: boolean = false;

  // Public observables for template consumption
  readonly files = this.files$.asObservable();
  readonly isResizing = this.isResizing$.asObservable();
  readonly currentView = this.currentView$.asObservable();
  readonly isPreviewLoading = this.isPreviewLoading$.asObservable();
  readonly previewIcon = this.previewIcon$.asObservable();
  readonly previewError = this.previewError$.asObservable();
  readonly errorDescription = this.errorDescription$.asObservable();
  readonly errorTerminalOutput = this.errorTerminalOutput$.asObservable();
  readonly currentTheme = this.currentTheme$.asObservable();
  readonly deployedUrl = this.deployedUrl$.asObservable();
  readonly isPanelCollapsed = this.isPanelCollapsed$.asObservable();
  readonly isLoading = this.isLoading$.asObservable();
  readonly isHistoryActive = this.isHistoryActive$.asObservable();
  readonly isCodeActive = this.isCodeActive$.asObservable();
  readonly isPreviewActive = this.isPreviewActive$.asObservable();
  readonly isLogsActive = this.isLogsActive$.asObservable();
  readonly isArtifactsActive = this.isArtifactsActive$.asObservable();
  readonly isLeftPanelCollapsed = this.isLeftPanelCollapsed$.asObservable();
  readonly minWidth = this.minWidth$.asObservable();
  readonly isExperienceStudioModalOpen = this.isExperienceStudioModalOpen$.asObservable();

  // Project name properties
  projectName: string | null = null;
  isProjectNameLoading: boolean = true;

  // Artifacts tab properties
  isArtifactsTabEnabled: boolean = false; // Start disabled, enable when artifacts are available
  // Artifacts data will be populated dynamically from polling responses
  artifactsData: any[] = [];

  // Track which artifacts have been loaded to ensure persistence
  private loadedArtifacts: Set<string> = new Set();

  // Track if artifacts tab should be visible (always true now)
  isArtifactsTabVisible: boolean = true;

  // Flags to track if specific artifacts are available
  hasLayoutAnalyzed: boolean = false;
  hasDesignSystem: boolean = false;
  selectedArtifactFile: any = null;

  // Typewriter effect properties for artifacts
  private typewriterTimeouts: { [key: string]: any } = {};
  private artifactTypingSpeed: number = 15; // Same speed as stepper

  // Track typewriter state for each artifact
  artifactTypewriterStates: { [key: string]: {
    visibleContent: string;
    isTyping: boolean;
    fullContent: string;
  } } = {};

  // Design system data
  designSystemData: any = null;
  designTokens: DesignToken[] = [];
  layoutAnalyzedData: any[] = [];

  // Progress state tracking
  currentProgressState: string = '';

  lastProgressDescription: string = '';
  pollingStatus: string = 'PENDING';

  // New polling response state tracking
  currentProgress: ProgressState | null = null;
  currentStatus: StatusType | null = null;
  newProgressDescription: string = '';
  newLogContent: string = '';
  newArtifactData: any = null;
  newFileList: string[] = [];
  newCodeFiles: FileData[] = [];
  newPreviewUrl: string = '';
  isNewPreviewEnabled: boolean = false;

  // Prompt bar enablement - only for DEPLOY + (COMPLETED | FAILED)
  isPromptBarEnabled: boolean = false;

  // Persistent tab visibility tracking - once shown, always shown
  private codeTabEverShown = false;
  private previewTabEverShown = false;

  // URL priority tracking - new polling response URLs override legacy URLs
  private hasNewPollingResponseUrl = false;

  // Enhanced iframe loading management
  private isIframeReady$ = new BehaviorSubject<boolean>(false);
  private isUrlValidated$ = new BehaviorSubject<boolean>(false);
  private urlValidationError$ = new BehaviorSubject<string>('');
  private isUrlAvailable$ = new BehaviorSubject<boolean>(false);

  // Preview URL for DEPLOYED state
  previewUrl: string = '';

  // Project information from new polling response
  currentProjectInfo: ProjectInfo | null = null;

  private autoSwitchToLogsTimer: any;

  // Default layout example images (fallback)
  layoutExampleImages: string[] = [
    'assets/images/01.png',
    'assets/images/02.png',
    'assets/images/03.png',
    'assets/images/04.png',
    'assets/images/05.png',
    'assets/images/06.png',
    'assets/images/07.png',
    'assets/images/08.png',
  ];

  // Layout mapping
  layoutMapping: { [key: string]: string } = {
    HB: 'Header + Body (No Sidebars, No Footer)',
    HBF: 'Header + Body + Footer (No Sidebars)',
    HLSB: 'Header + Left Sidebar + Body (No Footer)',
    HLSBF: 'Header + Left Sidebar + Body + Footer',
    HBRS: 'Header + Body + Right Sidebar (No Footer)',
    HBRSF: 'Header + Body + Right Sidebar + Footer',
    HLSBRS: 'Header + Left Sidebar + Body + Right Sidebar (No Footer)',
    HLSBRSF: 'Header + Left Sidebar + Body + Right Sidebar + Footer',
  };

  // Layout data directly from API response
  layoutData: string[] = ['HB', 'HBF'];

  // Loading state for layout cards
  isLayoutLoading: boolean = true;

  // ENHANCED: Track layout detection and progress transition for artifacts display
  private detectedLayoutFromPrevMetadata: string | null = null;
  private previousProgressState: string | null = null;
  private shouldShowLayoutArtifact = false;

  // ENHANCED: Error state management for FAILED status
  public isFailedState = false;
  private previewTabName$ = new BehaviorSubject<string>('Preview');

  // Selected image data URI
  selectedImageDataUri: string = '';

  // Chat messages
  lightMessages: {
    id?: string; // Unique identifier for text replacement
    text: string;
    from: 'user' | 'ai';
    theme: 'light';
    hasSteps?: boolean;
    imageDataUri?: string;
    isError?: boolean;
    errorDetails?: string;
    isErrorExpanded?: boolean;
    showIntroMessage?: boolean; // For UI Design intro message display
    originalText?: string; // Store original text for restoration
    // Enhanced loading state management properties
    showLoadingIndicator?: boolean; // Whether to show loading spinner/indicator
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error'; // Current phase of the generation process
    mainAPIInProgress?: boolean; // Whether main wireframe generation API is still running
  }[] = [];

  darkMessages: {
    text: string;
    from: 'user' | 'ai';
    theme: 'dark';
  }[] = [];

  lightPrompt: string = '';
  darkPrompt: string = '';
  // Status checking properties
  projectId: string | null = null;
  jobId: string | null = null;
  isPolling = false;
  isCodeGenerationComplete = false; // Track if code generation is complete
  isCodeTabEnabled = false; // Track if code tab is enabled
  isPreviewTabEnabled = false; // Track if preview tab is enabled
  isLogsTabEnabled = false; // Track if logs tab is enabled
  image = 'assets/images/history_card.png';
  title = 'Version 1 Application';
  appName: string | null = null; // Store the generated app name

  // Logs properties
  hasLogs: boolean = false;
  logMessages: string[] = [];
  formattedLogMessages: any[] = []; // Array to hold formatted logs with type, content, and path
  isStreamingLogs: boolean = false;
  isTypingLog: boolean = false; // Made public for template access
  currentLogIndex: number = 0; // Made public for template access
  private logStreamTimer: any;
  private currentCharIndex: number = 0;
  private processedLogHashes: Set<string> = new Set(); // Track processed logs by hash to avoid duplicates
  private lastProgressState: string = ''; // Track the last progress state to only show changes
  private expandedCodeLogs: Set<string> = new Set(); // Track which code logs are expanded
  private logsTabAutoEnabled: boolean = false; // Track if logs tab has been auto-enabled

  // Loading state messages
  loadingMessages = [
    'Analyzing your requirements and design specifications... 🔍',
    'Identifying key UI/UX patterns from your requirements... 🧩',
    'Mapping component relationships and dependencies... 🗺️',
    'Generating component architecture and file structure... 📂',
    'Planning optimal folder organization for scalability... 📁',
    'Creating responsive UI components with modern best practices... 🎨',
    'Implementing accessibility standards for inclusive design... ♿',
    'Building reusable component library for consistency... 🧰',
    'Implementing interactive elements and event handlers... 🖱️',
    'Adding form validation and user input handling... ✅',
    'Optimizing code for performance and maintainability... ⚙️',
    'Implementing lazy loading for improved initial load time... ⚡',
    'Adding CSS styling and layout configurations... 💅',
    'Creating responsive breakpoints for all device sizes... 📱',
    'Implementing data flow and state management... 🔄',
    'Setting up API integration and data fetching logic... 🔌',
    'Configuring error handling and fallback states... 🛡️',
    'Ensuring cross browser compatibility and responsive design... 🌐',
    'Implementing animations and transitions for better UX... ✨',
    'Optimizing assets and resources for faster loading... 🚄',
    'Finalizing code with proper documentation and comments... 📝',
    'Running code quality checks and optimizations... 🧹',
    'Preparing preview deployment for your application... 🚀',
    'Setting up build configuration for optimal performance... 🏗️',
    'Finalizing application bundle for deployment... 📦',
  ];

  /**
   * Transform loading messages to remove underscores and hyphens
   * This method is called in the constructor
   */
  private transformLoadingMessages(): void {
    // Apply text transformation to any messages that might still have underscores or hyphens
    this.loadingMessages = this.textTransformationService.transformMessages(this.loadingMessages);
  }

  generatedCode: any;
  editorReady = false;
  private subscription: Subscription | null = null;

  // Element selection properties
  selectedElement: any = null;
  showEditorIcon = false;
  urlSafe?: SafeResourceUrl;
  // Element selection class - commented out since it's not currently used
  // private lastSelectedElementClass = 'element-highlight';
  isElementSelectionMode = false;
  selectedElementHTML: string | null = null;
  selectedElementCSS: string | null = null;
  selectedElementPath: string | null = null;
  selectedElementId: string | null = null;
  selectedElementTagName: string | null = null;

  // Image preview properties
  showPreview: boolean = false;
  previewImage: { url: string, name: string } | null = null;

  // Reference to the chat-window component
  @ViewChild(ChatWindowComponent) chatWindow!: ChatWindowComponent;

  // Reference to the code-viewer component
  @ViewChild(CodeViewerComponent) codeViewer!: CodeViewerComponent;

  // Reference to the UI Design canvas
  @ViewChild('uiDesignCanvas', { static: false }) uiDesignCanvas!: ElementRef;

  // Canvas auto-centering properties
  private resizeTimeout: any;
  private canvasResizeObserver?: ResizeObserver;

  // Enhanced iframe loading observables
  readonly isIframeReady = this.isIframeReady$.asObservable();
  readonly isUrlValidated = this.isUrlValidated$.asObservable();
  readonly urlValidationError = this.urlValidationError$.asObservable();
  readonly isUrlAvailable = this.isUrlAvailable$.asObservable();

  constructor(
    private codeSharingService: CodeSharingService,
    private codeGenerationService: CodeGenerationService,
    private themeService: ThemeService,
    private cdr: ChangeDetectorRef,
    // Renderer2 is kept for potential future DOM manipulations but currently unused
    // private renderer2: Renderer2,
    public sanitizer: DomSanitizer,
    private pollingService: PollingService,
    private newPollingResponseProcessor: NewPollingResponseProcessorService,

    private appStateService: AppStateService,
    private ngZone: NgZone,
    private promptService: PromptBarService,
    private promptSubmissionService: PromptSubmissionService,
    private router: Router,
    private userSignatureService: UserSignatureService,
    private textTransformationService: TextTransformationService,
    private stepperStateService: StepperStateService,
    private toastService: ToastService,
    private vscodeExportService: VSCodeExportService,
    private fileTreePersistenceService: FileTreePersistenceService,
    private monacoStateManagementService: MonacoStateManagementService,
    private route: ActivatedRoute,
    private uiDesignCanvasService: UIDesignCanvasService,
    private uiDesignNodeService: UIDesignNodeService,
    private uiDesignViewportService: UIDesignViewportService,
    private generateUIDesignService: GenerateUIDesignService,
    private uiDesignSelectionService: UIDesignSelectionService,
    private uiDesignEditService: UIDesignEditService,
    private uiDesignNodePositioningService: UIDesignNodePositioningService,
    public uiDesignVisualFeedbackService: UIDesignVisualFeedbackService,
    public wireframeGenerationStateService: WireframeGenerationStateService,
    private filenameNormalizationService: FilenameNormalizationService,
    private uiDesignFilenameTransformerService: UIDesignFilenameTransformerService,
    private wireframeNodeManagementService: WireframeNodeManagementService,
    private uiDesignIntroService: UIDesignIntroService
  ) {
    // Initialize current theme using BehaviorSubject
    this.currentTheme$.next(this.themeService.getCurrentTheme());
    // Initialize icons based on the current theme

    // Transform loading messages to remove underscores and hyphens
    this.transformLoadingMessages();
  }

  /**
   * Show Netlify preview URL when deployment completes
   * Displays the deployed Netlify URL from the deployment response
   */
  // private showHardCodedPreview(): void {
  //   // Check if we have a Netlify URL from the deployment response
  //   const netlifyUrl = this.newPreviewUrl || 'https://mlo-a774-5c9a.netlify.app';


  //   // Set the URL and enable preview
  //   this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(netlifyUrl);
  //   this.deployedUrl$.next(netlifyUrl);
  //   this.newPreviewUrl = netlifyUrl;

  //   // Enable preview tab and clear any errors/loading
  //   this.isPreviewTabEnabled = true;
  //   this.previewError$.next(false);
  //   this.isPreviewLoading$.next(false);
  //   this.previewIcon$.next('bi-eye');
  //   this.isLoading$.next(false);

  //   // Switch to preview view
  //   this.togglePreviewView();

  // }

  /**
   * Navigate to the home/main page and reset all state
   */
  navigateToHome(): void {
    // this.logger.info('Navigating to home and resetting all state');

    // Reset logs state first
    this.pollingService.resetLogs();

    // Stop any active polling
    if (this.isPolling) {
      this.pollingService.stopPolling();
      this.isPolling = false;
    }

    // Reset the app state
    this.appStateService.resetProjectState();

    // Reset the prompt submission state
    this.promptSubmissionService.resetSubmissionState();

    // Reset the code sharing service state
    this.codeSharingService.resetState();

    // Clear any selected files or images in the prompt service
    this.promptService.setImage(null);
    this.promptService.setPrompt('');

    // Reset local component state
    this.resetComponentState();

    // Clear any existing toasts before showing a new one
    this.toastService.clear();

    // Show toast notification
    this.toastService.info('Returning to home page');

    // Navigate to the home page
    this.router.navigate(['/experience']);
  }

  /**
   * Reset all component state to initial values
   * This is a comprehensive reset of all local component state
   */
  private resetComponentState(): void {
    // Reset file and code state using BehaviorSubjects
    this.files$.next([]);
    this.isCodeGenerationComplete = false;
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);
    this.layoutData = [];
    this.generatedCode = null;
    this.editorReady = false;

    // Reset logs state
    this.logMessages = [];
    this.formattedLogMessages = [];
    this.hasLogs = false;
    this.isStreamingLogs = false;
    this.isTypingLog = false;
    this.currentLogIndex = 0;
    this.currentCharIndex = 0;
    this.processedLogHashes.clear();
    this.lastProgressState = '';
    this.expandedCodeLogs.clear();
    this.logsTabAutoEnabled = false; // Reset auto-enable flag for new session

    // Reset UI state using BehaviorSubjects
    this.selectedImageDataUri = '';
    this.currentProgressState = '';
    this.lastProgressDescription = '';
    this.pollingStatus = 'PENDING';
    this.userSelectedTab = false;
    this.isResizing$.next(false);
    this.isPanelCollapsed$.next(false);

    // Reset prompt bar state
    this.isPromptBarEnabled = false;
    this.isLoading$.next(true);
    this.isHistoryActive$.next(false);
    this.isLeftPanelCollapsed$.next(false);
    this.isExperienceStudioModalOpen$.next(false);

    // Reset tab states using BehaviorSubjects
    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(false);
    this.isPreviewTabEnabled = true;
    this.isLogsTabEnabled = true;
    this.isCodeTabEnabled = false;
    this.isArtifactsTabEnabled = false; // Start disabled, enable when artifacts are available

    // Reset artifacts state
    this.artifactsData = [];
    this.loadedArtifacts.clear();
    this.hasLayoutAnalyzed = false;
    this.hasDesignSystem = false;
    this.selectedArtifactFile = null;
    this.designSystemData = null;
    this.designTokens = [];
    this.layoutAnalyzedData = [];

    // ENHANCED: Reset layout tracking variables
    this.detectedLayoutFromPrevMetadata = null;
    this.previousProgressState = null;
    this.shouldShowLayoutArtifact = false;

    // ENHANCED: Reset error state and preview tab name
    this.isFailedState = false;
    this.previewTabName$.next('Preview');

    // Reset persistent tab visibility flags
    this.codeTabEverShown = false;
    this.previewTabEverShown = false;

    // Reset URL priority tracking
    this.hasNewPollingResponseUrl = false;

    // Reset view state using BehaviorSubject
    this.currentView$.next('preview');

    // Reset chat messages and prompt state
    this.lightMessages = [];
    this.darkMessages = [];
    this.lightPrompt = '';
    this.darkPrompt = '';

    // Clear any selected image in chat window
    this.selectedImageDataUri = '';

    // Reset error state using BehaviorSubjects
    this.previewError$.next(false);
    this.errorDescription$.next('Even the best systems have bad days sometimes. Please try again later.');
    this.errorTerminalOutput$.next('');

    // Reset project state
    this.projectId = null;
    this.jobId = null;
    this.appName = null;
    this.projectName = null;
    this.isProjectNameLoading = true;

    // Reset element selection state
    this.selectedElement = null;
    this.showEditorIcon = false;
    this.isElementSelectionMode = false;
    this.selectedElementHTML = null;
    this.selectedElementCSS = null;
    this.selectedElementPath = null;
    this.selectedElementId = null;
    this.selectedElementTagName = null;

    // Reset any timers
    if (this.autoSwitchToLogsTimer) {
      clearTimeout(this.autoSwitchToLogsTimer);
      this.autoSwitchToLogsTimer = null;
    }

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
      this.logStreamTimer = null;
    }

    // Clear design tokens update timer
    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
      this.designTokensUpdateTimer = null;
    }
  }

  /**
   * Opens the preview in a new browser tab
   */
  openPreviewInNewTab(): void {
    const currentUrl = this.deployedUrl$.value;
    if (currentUrl) {
      window.open(currentUrl, '_blank');
      this.toastService.info('Opening preview in new tab');
    } else {
      this.toastService.warning('Preview URL is not available yet');
    }
  }

  /**
   * Debug method to check current URL state
   * Can be called from browser console: window.codeWindow.debugUrlState()
   */
  debugUrlState(): void {
    // Debug information available via browser console
    // deployedUrl$ value: this.deployedUrl$.value
    // newPreviewUrl: this.newPreviewUrl
    // urlSafe: this.urlSafe
    // isPreviewTabEnabled: this.isPreviewTabEnabled
    // hasNewPollingResponseUrl: this.hasNewPollingResponseUrl
    // isNewPreviewEnabled: this.isNewPreviewEnabled
    // Current progress: this.newPollingResponseProcessor.getCurrentProgress()
    // Current status: this.newPollingResponseProcessor.getCurrentStatus()
  }

  /**
   * Shows the image preview overlay
   * @param imageUrl URL of the image to preview
   * @param imageName Name of the image (optional)
   */
  showImagePreview(imageUrl: string, imageName: string = 'Image'): void {
    this.previewImage = { url: imageUrl, name: imageName };
    this.showPreview = true;
    this.cdr.detectChanges();
  }

  /**
   * Closes the image preview overlay
   */
  closeImagePreview(): void {
    this.showPreview = false;
    this.previewImage = null;
    this.cdr.detectChanges();
  }



  // Azure URL generation removed - using Netlify URLs from deployment response

  /**
   * Process artifact data from the polling service
   * Enhanced to handle new metadata structure and ensure persistence
   * @param artifactData The artifact data to process
   */
  private processArtifactData(artifactData: any): void {
    if (!artifactData) {
      return;
    }

    this.logger.info('🎯 Processing artifact data:', artifactData);

    // Process based on artifact type (tab will be enabled after successful processing)
    switch (artifactData.type) {
      case 'readme':
        this.processProjectOverviewArtifact(artifactData);
        break;
      case 'layout':
        this.processLayoutAnalyzedArtifact(artifactData);
        break;
      case 'design-tokens':
        this.processDesignSystemArtifact(artifactData);
        break;
      default:
        this.logger.debug('Unknown artifact type:', artifactData.type);
    }

    // Force change detection to update the UI
    this.cdr.detectChanges();
  }

  /**
   * Enable artifacts tab only when artifacts are actually available
   * This ensures the tab is disabled until real artifacts are processed
   */
  private enableArtifactsTabIfNeeded(): void {
    const hasArtifacts = this.artifactsData.length > 0;
    this.isArtifactsTabEnabled = hasArtifacts;
  }

  /**
   * Process project overview artifact and ensure persistence
   */
  private processProjectOverviewArtifact(artifactData: any): void {
    const artifactName = 'Project Overview';

    // Check if already exists
    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    const artifactItem = {
      name: artifactName,
      type: 'markdown',
      content: artifactData.content || artifactData.data || 'Project overview content'
    };

    if (existingIndex !== -1) {
      // Update existing
      this.artifactsData[existingIndex] = artifactItem;
      this.logger.info('✅ Updated existing Project Overview artifact');
    } else {
      // Add new
      this.artifactsData.unshift(artifactItem); // Add at beginning
      this.logger.info('🆕 Added new Project Overview artifact');
    }

    // Start typewriter animation if this is the selected artifact and not already completed
    if (this.selectedArtifactFile && this.selectedArtifactFile.name === artifactName) {
      const existingState = this.artifactTypewriterStates[artifactName];
      // Only start typewriter if it hasn't been started or is incomplete
      if (!existingState || existingState.isTyping || existingState.visibleContent.length < artifactItem.content.length) {
        this.startArtifactTypewriter(artifactName, artifactItem.content);
      }
    }

    // Mark as loaded for persistence
    this.loadedArtifacts.add(artifactName);

    // Enable artifacts tab now that we have artifacts
    this.enableArtifactsTabIfNeeded();

    // Select if no artifact is currently selected
    if (!this.selectedArtifactFile) {
      this.selectArtifactFile(artifactItem);
    }
  }

  /**
   * Prevent duplicate Layout Analyzed artifacts
   */
  private preventDuplicateLayoutAnalyzed(): boolean {
    const existingCount = this.artifactsData.filter(item => item.name === 'Layout Analyzed').length;
    if (existingCount > 1) {
      // Remove all but the first one
      const firstIndex = this.artifactsData.findIndex(item => item.name === 'Layout Analyzed');
      this.artifactsData = this.artifactsData.filter((item, index) =>
        item.name !== 'Layout Analyzed' || index === firstIndex
      );
      this.logger.info('🧹 Removed duplicate Layout Analyzed artifacts, kept only one');
      return true;
    }
    return false;
  }

  /**
   * Process layout analyzed artifact and ensure persistence
   * ENHANCED: Only shows artifact when progress has moved FROM LAYOUT_ANALYZED to next state
   */
  private processLayoutAnalyzedArtifact(artifactData: any): void {
    const artifactName = 'Layout Analyzed';

    // Prevent duplicates first
    this.preventDuplicateLayoutAnalyzed();

    this.logger.info('🎯 processLayoutAnalyzedArtifact called with:', artifactData);

    // ENHANCED: Check current progress state
    const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
    this.logger.info('📊 Current progress state:', currentProgress);

    // ENHANCED: Only show Layout Analyzed artifact when progress has moved FROM LAYOUT_ANALYZED
    if (currentProgress === 'LAYOUT_ANALYZED' || currentProgress === 'Layout Analyzed') {
      this.logger.info('🚫 Currently in LAYOUT_ANALYZED state - storing layout but NOT showing in artifacts');

      // Store the layout data for later use but don't add to artifacts
      this.detectedLayoutFromPrevMetadata = artifactData.layoutKey || artifactData.layoutCode;
      this.shouldShowLayoutArtifact = false;

      // Update layoutData for preview display
      let layoutKey = artifactData.layoutKey || artifactData.layoutCode;
      if (!layoutKey || !this.layoutMapping[layoutKey]) {
        layoutKey = 'HB'; // Default fallback
      }
      this.layoutData = [layoutKey];

      this.logger.info('✅ Layout stored for later display:', this.detectedLayoutFromPrevMetadata);
      return; // Don't add to artifacts during LAYOUT_ANALYZED state
    }

    // ENHANCED: Only process if we have detected layout and are no longer in LAYOUT_ANALYZED state
    if (!this.detectedLayoutFromPrevMetadata) {
      this.logger.warn('⚠️ No stored layout detected from prev_metadata');
      return;
    }

    this.logger.info('🎯 Progress moved from LAYOUT_ANALYZED - now showing Layout Analyzed artifact');

    // Check if already exists
    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    // ENHANCED: Use stored layout key
    let layoutKey = this.detectedLayoutFromPrevMetadata;

    // Validate layout key against known mappings
    if (!layoutKey || !this.layoutMapping[layoutKey]) {
      layoutKey = 'HB'; // Default fallback
      this.logger.warn('Invalid layout key, using default HB layout');
    } else {
      this.logger.info('✅ Valid layout key detected:', layoutKey);
    }

    // Update layoutData for component state
    this.layoutData = [layoutKey];

    // Get layout image URL from the validated layout key
    const layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    const layoutName = this.layoutMapping[layoutKey];

    this.logger.info('🖼️ Creating layout artifact with:', { layoutKey, layoutImageUrl, layoutName });

    const artifactItem = {
      name: artifactName,
      type: 'image',
      content: layoutImageUrl
    };

    if (existingIndex !== -1) {
      // Update existing
      this.artifactsData[existingIndex] = artifactItem;
      this.logger.info('✅ Updated existing Layout Analyzed artifact');
    } else {
      // Add new
      this.artifactsData.push(artifactItem);
      this.logger.info('🆕 Added new Layout Analyzed artifact');
    }

    // CRITICAL: Update layoutAnalyzedData for artifacts display
    this.layoutAnalyzedData = [{
      key: layoutKey,
      name: layoutName,
      imageUrl: layoutImageUrl
    }];

    this.logger.info('🎯 Updated layoutAnalyzedData:', this.layoutAnalyzedData);

    // Mark as loaded for persistence
    this.loadedArtifacts.add(artifactName);
    this.hasLayoutAnalyzed = true;
    this.shouldShowLayoutArtifact = true;

    // Enable artifacts tab now that we have artifacts
    this.enableArtifactsTabIfNeeded();

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  /**
   * Process design system artifact and ensure persistence
   */
  private processDesignSystemArtifact(artifactData: any): void {
    const artifactName = 'Design System';

    // Check if already exists
    const existingIndex = this.artifactsData.findIndex(item => item.name === artifactName);

    const artifactItem = {
      name: artifactName,
      type: 'component',
      content: 'Design system tokens and components',
      tokens: artifactData.tokens
    };

    if (existingIndex !== -1) {
      // Update existing
      this.artifactsData[existingIndex] = artifactItem;
      this.logger.info('✅ Updated existing Design System artifact');
    } else {
      // Add new
      this.artifactsData.push(artifactItem);
      this.logger.info('🆕 Added new Design System artifact');
    }

    // Mark as loaded for persistence
    this.loadedArtifacts.add(artifactName);
    this.hasDesignSystem = true;

    // Enable artifacts tab now that we have artifacts
    this.enableArtifactsTabIfNeeded();

    // Initialize design tokens if provided
    if (artifactData.tokens) {
      this.designSystemData = artifactData.tokens;

      // ENHANCED: Check if this is a fallback design system
      if (artifactData.isFallback) {
        this.logger.info('Design system fallback processed - showing Coming Soon placeholders');
      }

      this.initializeDesignTokens();
    }
  }

  /**
   * Legacy method - keeping for backward compatibility
   * @deprecated Use processArtifactData instead
   */
  private processArtifactDataLegacy(artifactData: any): void {
    if (!artifactData) return;

    // Check if the artifact data has a 'data' field
    if (artifactData.data) {
      // Get the current progress state
      const currentState = this.currentProgressState;

      // Process the data based on the current progress state
      if (currentState === StepperState.OVERVIEW || !currentState) {
        // Enable the Artifacts tab when Project Overview data is received
        this.isArtifactsTabEnabled = true;

        // Update the Project Overview artifact - NEVER add duplicates
        const projectOverviewIndex = this.artifactsData.findIndex(item => item.name === 'Project Overview');
        if (projectOverviewIndex !== -1) {
          // Update existing Project Overview
          this.artifactsData[projectOverviewIndex].content = artifactData.data;
          // this.logger.info('✅ Updated existing Project Overview artifact with new data (legacy method)');
        } else {
          // Only add new if none exists (should not happen with our initialization)
          // this.artifactsData.push({
          //   name: 'Project Overview',
          //   type: 'markdown',
          //   content: artifactData.data
          // });
          // this.logger.info('🆕 Added new Project Overview artifact (legacy method - fallback)');
        }
      } else if (currentState === StepperState.LAYOUT_ANALYZED) {
        // Update or add Layout Analyzed artifact - NEVER add duplicates
        const layoutAnalyzedIndex = this.artifactsData.findIndex(item => item.name === 'Layout Analyzed');
        if (layoutAnalyzedIndex !== -1) {
          // Update existing Layout Analyzed
          this.artifactsData[layoutAnalyzedIndex].content = artifactData.data;
          // this.logger.info('✅ Updated existing Layout Analyzed artifact with new data');
        } else {
          // Only add new if none exists
          this.artifactsData.push({
            name: 'Layout Analyzed',
            type: 'image',
            content: artifactData.data
          });
          // this.logger.info('🆕 Added new Layout Analyzed artifact');
          this.hasLayoutAnalyzed = true;
        }
      } else if (currentState === StepperState.DESIGN_SYSTEM_MAPPED) {
        // Update or add Design System artifact - NEVER add duplicates
        const designSystemIndex = this.artifactsData.findIndex(item => item.name === 'Design System');
        if (designSystemIndex !== -1) {
          // Update existing Design System
          this.artifactsData[designSystemIndex].content = artifactData.data;
          // this.logger.info('✅ Updated existing Design System artifact with new data');
        } else {
          // Only add new if none exists
          this.artifactsData.push({
            name: 'Design System',
            type: 'component',
            content: artifactData.data
          });
          // this.logger.info('🆕 Added new Design System artifact');
          this.hasDesignSystem = true;
        }
      } else {
        // For other states, add a generic artifact with the state name - NEVER add duplicates
        const stateName = this.getDisplayNameForState(currentState);
        const stateIndex = this.artifactsData.findIndex(item => item.name === stateName);
        if (stateIndex !== -1) {
          // Update existing artifact
          this.artifactsData[stateIndex].content = artifactData.data;
          // this.logger.info(`✅ Updated existing ${stateName} artifact with new data`);
        } else {
          // Only add new if none exists
          this.artifactsData.push({
            name: stateName,
            type: 'markdown',
            content: artifactData.data
          });
          // this.logger.info(`🆕 Added new ${stateName} artifact`);
        }
      }

      // Force change detection to update the UI
      this.cdr.detectChanges();
    }
  }

  /**
   * Get a display name for a progress state
   * @param state The progress state
   * @returns A display name for the state
   */
  private getDisplayNameForState(state: string): string {
    // Convert the state to a more readable format
    // e.g., LAYOUT_ANALYZED -> Layout Analyzed
    if (!state) return 'Unknown State';

    return state
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  ngOnInit() {
    // Initialize component - Netlify URL will be set when deployment completes

    // Reset component state
    this.resetComponentState();

    // Initialize with default prompt if no prompt is available
    this.initializeDefaultPrompt();

    // Initialize app state and subscriptions
    this.initializeAppState();

    // Select the Design System file by default if available, otherwise select the first file
    if (this.artifactsData.length > 0 && !this.selectedArtifactFile) {
      const designSystemFile = this.artifactsData.find(file => file.name === 'Design System');
      if (designSystemFile) {
        this.selectArtifactFile(designSystemFile);
      } else {
        this.selectArtifactFile(this.artifactsData[0]);
      }
    }

    // Initialize design tokens for the Design System
    this.initializeDesignTokens();

    // OLD POLLING RESPONSE FUNCTIONS - COMMENTED OUT
    // Subscribe to artifact data from polling service
    // if (this.subscription) {
    //   this.subscription.add(
    //     this.pollingService.artifactData$.subscribe(artifactData => {
    //       if (artifactData) {
    //         this.processArtifactData(artifactData);
    //       }
    //     })
    //   );
    // } else {
    //   // If subscription is null, create a new one
    //   this.subscription = this.pollingService.artifactData$.subscribe(artifactData => {
    //     if (artifactData) {
    //       this.processArtifactData(artifactData);
    //     }
    //   });
    // }

    // Subscribe to new polling response processor observables
    this.subscribeToNewPollingProcessor();

    // Detect UI Design mode from route data
    this.initializeUIDesignMode();

    // Subscribe to UI Design service for overview tab management
    this.subscribeToUIDesignService();

    // Simulate loading the project name with shimmer effect
    this.isProjectNameLoading = true;
    setTimeout(() => {
      this.isProjectNameLoading = false;
      this.cdr.detectChanges();
    }, 10000); // Show shimmer for 10 seconds before displaying the project name
  }

  /**
   * Initialize UI Design mode detection and setup
   */
  private initializeUIDesignMode(): void {
    // Subscribe to route data to detect UI Design mode
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(data => {
      const isUIDesignMode = data['cardType'] === 'Generate UI Design';
      const wasUIDesignMode = this.isUIDesignMode$.value;

      // If switching away from UI Design mode, clean up first
      if (wasUIDesignMode && !isUIDesignMode) {
        this.cleanupUIDesignMode();
      }

      this.isUIDesignMode$.next(isUIDesignMode);

      if (isUIDesignMode) {
        this.logger.info('🎨 UI Design mode detected - initializing isolated canvas');
        this.setupUIDesignMode();
      } else {
        this.logger.info('💻 Standard mode detected - using default tabs');
      }
    });
  }

  /**
   * Setup UI Design mode configuration
   */
  private setupUIDesignMode(): void {
    this.logger.info('🎨 Setting up UI Design mode with complete isolation');

    // ===== COMPLETE UI ISOLATION =====

    // 1. Hide ALL other tabs and disable their functionality
    this.isLogsTabEnabled = false;
    this.isArtifactsTabEnabled = false;
    this.isCodeActive$.next(false);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(false);

    // 2. Set preview as the ONLY available tab
    this.currentView$.next('preview');
    this.isPreviewActive$.next(true);

    // 3. Disable polling and other API services for Generate Application workflow
    this.isPolling = false;
    this.pollingStatus = 'PENDING';
    this.currentProgressState = '';
    this.lastProgressDescription = '';

    // 4. Clear any existing Generate Application state
    this.isCodeGenerationComplete = false;
    this.files$.next([]);
    this.artifactsData = [];

    // 5. Initialize UI Design specific state
    this.isUIDesignMode$.next(true);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignRegenerating$.next(false);

    // Clear ALL existing loading nodes (comprehensive cleanup)
    this.clearAllLoadingNodes();

    // Reset wireframe generation state service
    this.wireframeGenerationStateService.reset();

    // 6. Initialize chat messages with UI Design prompt
    this.initializeUIDesignChatMessages();

    // 7. Initialize UI Design selection and editing services
    this.initializeUIDesignSelection();

    // ===== UI DESIGN CANVAS INITIALIZATION =====

    // Initialize UI Design canvas services
    this.initializeUIDesignCanvas();

    // Start UI Design generation flow (isolated from other APIs)
    this.startUIDesignGeneration();

    // Setup auto-centering after view initialization
    setTimeout(() => {
      this.setupAutoCanvasCentering();
    }, 200);

    this.logger.info('✅ UI Design mode setup complete - fully isolated from other workflows');
  }

  /**
   * Clean up UI Design mode state when navigating away
   */
  private cleanupUIDesignMode(): void {
    this.logger.info('🧹 Cleaning up UI Design mode state');

    // Reset UI Design state
    this.isUIDesignMode$.next(false);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isUIDesignRegenerating$.next(false);

    // Clear ALL loading nodes (comprehensive cleanup)
    this.clearAllLoadingNodes();

    // Reset intro service state
    this.uiDesignIntroService.resetIntroState();

    // Clear UI Design nodes
    this.uiDesignNodes$.next([]);
    this.selectedUIDesignNode$.next(null);
    this.isUIDesignFullScreenOpen$.next(false);

    // Clear selection state
    this.uiDesignSelectionService.reset();
    this.showCanvasTooltip$.next(true);
    this.isEditingUIDesign$.next(false);

    // Re-enable standard tabs
    this.isLogsTabEnabled = true;
    this.isArtifactsTabEnabled = true;

    this.logger.info('✅ UI Design mode cleanup complete with comprehensive loading node cleanup');
  }

  /**
   * Initialize UI Design selection and editing services
   */
  private initializeUIDesignSelection(): void {
    this.logger.info('🎯 Initializing UI Design selection and editing services');

    // Reset selection state
    this.uiDesignSelectionService.reset();
    this.isEditingUIDesign$.next(false);

    // Subscribe to tooltip visibility changes
    this.uiDesignSelectionService.showCanvasTooltip
      .pipe(takeUntil(this.destroy$))
      .subscribe(showTooltip => {
        this.showCanvasTooltip$.next(showTooltip);
        this.logger.info(`🔄 Canvas tooltip visibility: ${showTooltip}`);
      });

    // Subscribe to multi-selection changes
    this.uiDesignSelectionService.selectedNodes
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedNodes => {
        const hasSelection = selectedNodes.length > 0;

        this.logger.info('🎯 Multi-node selection changed:', {
          hasSelection,
          selectedCount: selectedNodes.length,
          nodeIds: selectedNodes.map(node => node.nodeId),
          fileNames: selectedNodes.map(node => node.fileName),
          isMultiSelectMode: this.uiDesignSelectionService.getIsMultiSelectMode()
        });

        // Update visual selection state
        this.updateNodeSelectionVisuals();
      });

    // Also subscribe to legacy single selection for backward compatibility
    this.uiDesignSelectionService.selectedNode
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedNode => {
        // This is mainly for logging and backward compatibility
        if (selectedNode) {
          this.logger.info('🎯 Legacy single selection updated:', {
            nodeId: selectedNode.nodeId,
            fileName: selectedNode.fileName
          });
        }
      });

    // Subscribe to editing state changes
    this.uiDesignSelectionService.isEditingInProgress
      .pipe(takeUntil(this.destroy$))
      .subscribe(isEditing => {
        this.isEditingUIDesign$.next(isEditing);
        this.logger.info(`🔄 Editing state changed: ${isEditing}`);
      });

    this.logger.info('✅ UI Design selection and editing services initialized');
  }

  /**
   * Subscribe to UI Design service for overview tab management
   */
  private subscribeToUIDesignService(): void {
    this.logger.info('🔗 Subscribing to UI Design service for overview tab management');

    // TODO: Re-enable when GenerateUIDesignService has proper observables
    // Subscribe to UI Design response data
    this.generateUIDesignService.uiDesignResponse$
      .pipe(takeUntil(this.destroy$))
      .subscribe(responseData => {
        if (responseData) {
          this.logger.info('📱 UI Design response received, updating overview tab:', responseData);
          this.handleUIDesignResponseForOverview(responseData);
        }
      });

    // Subscribe to overview tab visibility
    this.generateUIDesignService.showOverviewTab$
      .pipe(takeUntil(this.destroy$))
      .subscribe(show => {
        this.showUIDesignOverviewTab$.next(show);
        if (show) {
          this.logger.info('📱 Overview tab enabled, switching to overview view');
          // Automatically switch to overview tab when it becomes available
          this.currentView$.next('overview');
        }
      });
  }

  /**
   * Handle UI Design response data for overview tab
   */
  private handleUIDesignResponseForOverview(responseData: any): void {
    this.logger.info('🔄 Processing UI Design response for overview tab');

    try {
      // Extract pages from response
      const pages: MobilePage[] = [];

      if (responseData.pages && Array.isArray(responseData.pages)) {
        responseData.pages.forEach((page: any) => {
          if (page.fileName && page.content) {
            pages.push({
              fileName: page.fileName,
              content: page.content
            });
          }
        });
      }

      if (pages.length > 0) {
        this.logger.info(`📱 Found ${pages.length} pages for overview tab`);
        this.uiDesignPages$.next(pages);
        this.currentUIDesignPageIndex$.next(0);
        this.showUIDesignOverviewTab$.next(true);
      } else {
        this.logger.warn('⚠️ No valid pages found in UI Design response');
      }
    } catch (error) {
      this.logger.error('❌ Error processing UI Design response for overview:', error);
    }
  }

  /**
   * Initialize chat messages with UI Design prompt data
   * Enhanced with message persistence and duplicate prevention
   * Modified to use clean loading indicator instead of hardcoded messages
   */
  private initializeUIDesignChatMessages(): void {
    this.logger.info('💬 Initializing UI Design chat messages with clean loading indicator');

    // CRITICAL: DO NOT clear existing messages - only remove specific loading messages
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage = msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage = msg.from === 'ai' && msg.id && (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      // Keep all AI messages, remove only temporary loading messages
      return !isTemporaryLoadingMessage || isAIMessage;
    });

    // Clean up any legacy messages that might interfere
    this.cleanupLegacyEditMessages();

    // Try to get prompt from multiple sources in order of preference
    let userPrompt = '';
    let promptSource = '';

    // 1. Try to get from generateUIDesignService (most specific)
    const uiDesignPrompt = this.generateUIDesignService.getPromptData();
    if (uiDesignPrompt && uiDesignPrompt.trim()) {
      userPrompt = uiDesignPrompt;
      promptSource = 'generateUIDesignService';
    }

    // 2. Fallback to appStateService (from routing/navigation)
    if (!userPrompt) {
      const projectState = this.appStateService.getState().project;
      if (projectState.prompt && projectState.prompt.trim()) {
        userPrompt = projectState.prompt;
        promptSource = 'appStateService';
      }
    }

    // 3. Fallback to promptService (legacy support)
    if (!userPrompt) {
      try {
        // Subscribe to promptData$ to get current value
        this.promptService.promptData$.pipe(take(1)).subscribe(promptData => {
          if (promptData && typeof promptData === 'object' && 'prompt' in promptData) {
            const prompt = (promptData as any).prompt;
            if (prompt && typeof prompt === 'string' && prompt.trim()) {
              userPrompt = prompt;
              promptSource = 'promptService';
            }
          }
        });
      } catch (error) {
        this.logger.warn('⚠️ Error accessing promptService data:', error);
      }
    }

    if (userPrompt) {
      this.logger.info(`📝 Found UI Design prompt from ${promptSource}:`, userPrompt);

      // FIXED: Check if user message already exists to prevent duplication
      const existingUserMessage = this.lightMessages.find(msg =>
        msg.from === 'user' && msg.text === userPrompt
      );

      if (!existingUserMessage) {
        // Generate unique ID for user message to prevent conflicts
        const userMessageId = `user-prompt-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        // Add user prompt message only if it doesn't already exist
        this.lightMessages.push({
          id: userMessageId,
          text: userPrompt,
          from: 'user',
          theme: 'light'
        });
        this.logger.info('📝 Added user prompt message to chat with ID:', userMessageId);
      } else {
        this.logger.info('🚫 Skipped adding user prompt - already exists in chat');
      }

      // Add AI response message for UI Design workflow with intro message support
      const aiMessageId = `ai-generation-${Date.now()}`;

      // CRITICAL: Check for duplicate AI messages before adding
      const existingAIMessage = this.lightMessages.find(msg =>
        msg.from === 'ai' &&
        msg.id &&
        msg.id.startsWith('ai-generation-') &&
        this.activeAIMessageIds.has(msg.id)
      );

      if (!existingAIMessage) {
        // Create AI message with loading state - will be replaced by intro API response
        this.lightMessages.push({
          id: aiMessageId,
          text: '', // Start with empty text - will be populated by intro API
          from: 'ai',
          theme: 'light',
          showIntroMessage: true,
          showLoadingIndicator: true,
          loadingPhase: 'intro',
          mainAPIInProgress: true
        });

        // Track the AI message for duplicate prevention
        this.activeAIMessageIds.add(aiMessageId);
        this.currentActiveMessageId = aiMessageId;

        this.logger.info('📝 Created AI loading message with ID:', aiMessageId);
        this.logger.info('📝 AI message will be populated by intro API response');
      } else {
        this.logger.warn('🚫 Duplicate AI generation message prevented:', existingAIMessage.id);
        this.currentActiveMessageId = existingAIMessage.id || null;
      }

      this.logger.info('✅ UI Design chat messages initialized successfully');
      this.logger.info('📝 Created AI message with ID:', aiMessageId);
      this.logger.info('📝 Total messages in lightMessages:', this.lightMessages.length);
      this.logger.info('📝 Message IDs:', this.lightMessages.map(msg => msg.id));
    } else {
      this.logger.warn('⚠️ No UI Design prompt found in any service');

      // COMMENTED OUT: Fallback message replaced with clean loading indicator
      // this.lightMessages.push({
      //   text: "Welcome to UI Design generation! I'm ready to create wireframes based on your requirements.",
      //   from: 'ai',
      //   theme: 'light'
      // });
    }

    // Set loading state for clean loading indicator
    this.isUIDesignLoading$.next(true);

    // Force change detection to update the chat window
    this.cdr.detectChanges();
  }

  /**
   * Check if we should show the UI Design loading indicator
   * Returns true when UI Design mode is active and generation/regeneration is in progress
   */
  shouldShowUIDesignLoadingIndicator(): boolean {
    const isUIDesignMode = this.isUIDesignMode$.value;
    const isGenerating = this.isUIDesignGenerating$.value;
    const isRegenerating = this.isUIDesignRegenerating$.value;
    const isApiInProgress = this.uiDesignApiInProgress$.value;

    return isUIDesignMode && (isGenerating || isRegenerating || isApiInProgress);
  }

  /**
   * Start UI Design generation flow with parallel API calls and contextual messaging
   */
  private startUIDesignGeneration(): void {
    this.logger.info('🎨 Starting UI Design generation flow with parallel API calls');

    // Set UI Design generating state
    this.isUIDesignGenerating$.next(true);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);

    // Clear any existing nodes
    this.uiDesignNodes$.next([]);

    // Create loading nodes instead of demo data
    this.createLoadingNodes();

    // Get UI Design data from service
    const uiDesignData = this.generateUIDesignService.getUIDesignData();

    if (uiDesignData) {
      this.logger.info('🎯 Found UI Design data, starting parallel API calls:', uiDesignData);

      // Start parallel API calls (main generation + intro)
      this.initiateParallelUIDesignAPICalls(uiDesignData);
    } else {
      this.logger.warn('⚠️ No UI Design data found, showing error state');
      this.showUIDesignError('No UI Design data found. Please go back and submit a prompt.');
    }
  }

  /**
   * Create loading nodes to show while API is in progress
   */
  private createLoadingNodes(): void {
    this.logger.info('🔄 Creating loading nodes');

    // 🎯 FIXED: Create only 1 loading node for UI Design generation
    const loadingNodes: UIDesignNode[] = [
      {
        id: 'ui-design-loading-node',
        type: 'ui-design',
        data: {
          title: 'Generating UI Design...',
          htmlContent: this.sanitizer.bypassSecurityTrustHtml(''),
          rawContent: '',
          width: 300,
          height: 400,
          isLoading: true
        },
        position: { x: 0, y: 0 }, // Centered position
        selected: false,
        dragging: false,
        visible: true
      }
    ];

    // Set the single loading node
    this.uiDesignNodes$.next(loadingNodes);
    this.logger.info('✅ Single loading node created and displayed for UI Design generation');
  }

  /**
   * Initiate parallel UI Design API calls (main generation + intro)
   */
  private initiateParallelUIDesignAPICalls(uiDesignData: any): void {
    this.logger.info('🚀 Initiating parallel UI Design API calls (main + intro)');

    // Set API in progress state
    this.uiDesignApiInProgress$.next(true);
    this.uiDesignError$.next(null);

    // Start wireframe generation in state service
    this.wireframeGenerationStateService.startGeneration();

    // Build the API request using the simplified payload format
    const apiRequest = this.generateUIDesignService.buildAPIRequest();

    this.logger.info('📡 Calling wireframe-generation/generate API with payload:', apiRequest);

    // Create the main generation API call observable
    const mainAPICall = this.generateUIDesignService.generateUIDesign(apiRequest);

    // Get user request for intro API
    const userRequest = uiDesignData.prompt || '';

    // Execute parallel API calls using the intro service with text replacement
    this.uiDesignIntroService.executeParallelGeneration(userRequest, mainAPICall, this.currentActiveMessageId || undefined)
      .subscribe({
        next: (result) => {
          this.logger.info('✅ Parallel API calls completed:', result);

          if (result.mainAPISuccess) {
            this.handleUIDesignSuccess(result.mainAPIResult);
            // Complete text replacement when main API succeeds
            this.uiDesignIntroService.completeTextReplacement();
          } else {
            this.handleUIDesignFailure(new Error('Main API failed'));
          }
        },
        error: (error) => {
          this.logger.error('❌ Parallel API calls failed:', error);
          this.handleUIDesignFailure(error);
        }
      });

    // Subscribe to intro message state updates for text replacement
    this.uiDesignIntroService.introMessageState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: IntroMessageState) => {
        this.logger.info('🔄 Intro message state updated:', state);
        this.introMessageState$.next(state);

        // Handle text replacement in chat messages
        if (state.shouldReplaceText && state.targetMessageId && state.text) {
          this.handleChatMessageTextReplacement(state);
        }

        this.cdr.markForCheck();
      });
  }

  /**
   * Handle text replacement in chat messages with enhanced loading state management
   * Enhanced with duplicate prevention and active message validation
   */
  private handleChatMessageTextReplacement(state: IntroMessageState): void {
    this.logger.info('🔄 Attempting text replacement with state:', {
      targetMessageId: state.targetMessageId,
      textLength: state.text?.length || 0,
      shouldReplaceText: state.shouldReplaceText,
      activeMessageIds: Array.from(this.activeAIMessageIds),
      totalMessages: this.lightMessages.length,
      currentActiveMessageId: this.currentActiveMessageId
    });

    if (!state.targetMessageId || !state.text) {
      this.logger.warn('🚫 Text replacement blocked - missing targetMessageId or text:', {
        hasTargetId: !!state.targetMessageId,
        hasText: !!state.text,
        textPreview: state.text?.substring(0, 50)
      });
      return;
    }

    // CRITICAL: Only allow text replacement for active messages
    if (!this.activeAIMessageIds.has(state.targetMessageId)) {
      this.logger.warn('🚫 Text replacement blocked - message is not active:', state.targetMessageId);
      this.logger.warn('📝 Active message IDs:', Array.from(this.activeAIMessageIds));
      this.logger.warn('📝 All message IDs:', this.lightMessages.map(msg => ({
        id: msg.id,
        from: msg.from,
        text: msg.text?.substring(0, 50)
      })));
      return;
    }

    // Find the target message in lightMessages
    const targetMessage = this.lightMessages.find(msg => msg.id === state.targetMessageId);
    if (!targetMessage) {
      this.logger.warn('⚠️ Target message not found for text replacement:', state.targetMessageId);
      this.logger.warn('📝 Available message IDs:', this.lightMessages.map(msg => msg.id));
      this.logger.warn('📝 Total messages:', this.lightMessages.length);
      this.logger.warn('📝 Current active message ID:', this.currentActiveMessageId);
      return;
    }

    // Store original text if not already stored (for empty loading messages)
    if (!targetMessage.originalText) {
      targetMessage.originalText = targetMessage.text || '';
    }

    // Replace the message text with intro text and update loading state
    targetMessage.text = state.text;
    targetMessage.showLoadingIndicator = state.showLoadingIndicator;
    targetMessage.loadingPhase = state.loadingPhase;
    targetMessage.mainAPIInProgress = state.mainAPIInProgress;

    this.logger.info('✅ Text replaced in chat message with loading state:', {
      messageId: state.targetMessageId,
      textLength: state.text.length,
      showLoadingIndicator: state.showLoadingIndicator,
      loadingPhase: state.loadingPhase,
      mainAPIInProgress: state.mainAPIInProgress,
      originalText: targetMessage.originalText?.substring(0, 50),
      newText: targetMessage.text?.substring(0, 50)
    });

    // Trigger change detection
    this.cdr.markForCheck();
  }

  /**
   * Clean up completed regeneration session
   */
  private cleanupRegenerationSession(sessionId: string): void {
    if (this.activeRegenerationSessions.has(sessionId)) {
      const session = this.activeRegenerationSessions.get(sessionId);
      if (session) {
        // Remove from active AI message IDs
        this.activeAIMessageIds.delete(session.messageId);

        // Clear current active message ID if it matches this session
        if (this.currentActiveMessageId === session.messageId) {
          this.currentActiveMessageId = null;
        }

        // Remove the session
        this.activeRegenerationSessions.delete(sessionId);

        this.logger.info('🧹 Cleaned up regeneration session:', sessionId);
        this.logger.info('📝 Remaining active sessions:', this.activeRegenerationSessions.size);
      }
    }
  }

  /**
   * Restore original text in chat messages when text replacement is complete and clear all loading states
   * Enhanced with proper message lifecycle management and regeneration session cleanup
   */
  private restoreOriginalChatMessageText(): void {
    if (!this.currentActiveMessageId) {
      return;
    }

    // Find the target message
    const targetMessage = this.lightMessages.find(msg => msg.id === this.currentActiveMessageId);
    if (targetMessage && targetMessage.originalText) {
      // Restore original text
      targetMessage.text = targetMessage.originalText;
      delete targetMessage.originalText;

      // Clear all loading state properties
      targetMessage.showLoadingIndicator = false;
      targetMessage.loadingPhase = 'completed';
      targetMessage.mainAPIInProgress = false;

      this.logger.info('🔄 Original text restored and loading states cleared in chat message:', this.currentActiveMessageId);

      // ENHANCED: Clean up regeneration session if this is a regeneration message
      if (this.currentActiveMessageId.startsWith('ai-regeneration-')) {
        // Extract session ID from message ID
        const sessionId = this.currentActiveMessageId.replace('ai-regeneration-', '');
        this.cleanupRegenerationSession(sessionId);
      } else {
        // Legacy cleanup for non-regeneration messages
        this.activeAIMessageIds.delete(this.currentActiveMessageId);
        this.currentActiveMessageId = null;
      }

      this.logger.info('📝 Message lifecycle completed and cleaned up');

      // Trigger change detection
      this.cdr.markForCheck();
    }
  }

  /**
   * Initiate UI Design API call - ISOLATED from other APIs (legacy method)
   * @deprecated Use initiateParallelUIDesignAPICalls instead
   */
  private initiateUIDesignAPICall(uiDesignData: any): void {
    this.logger.info('🚀 Initiating wireframe-generation/generate API call - isolated workflow');

    // Set API in progress state
    this.uiDesignApiInProgress$.next(true);
    this.uiDesignError$.next(null);

    // Start wireframe generation in state service
    this.wireframeGenerationStateService.startGeneration();

    // Build the API request using the simplified payload format
    const apiRequest = this.generateUIDesignService.buildAPIRequest();

    this.logger.info('📡 Calling wireframe-generation/generate API with payload:', apiRequest);

    // Call the wireframe-generation/generate API (isolated from other APIs)
    this.generateUIDesignService.generateUIDesign(apiRequest)
      .subscribe({
        next: (response) => {
          this.logger.info('✅ Wireframe generation API success:', response);
          this.handleUIDesignSuccess(response);
        },
        error: (error) => {
          this.logger.error('❌ Wireframe generation API failed:', error);
          this.handleUIDesignFailure(error);
        }
      });
  }

  /**
   * Handle successful UI Design API response
   */
  private handleUIDesignSuccess(response: any): void {
    this.logger.info('🎨 Processing successful wireframe generation response');

    // Update state
    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.uiDesignError$.next(null);
    this.isUIDesignLoading$.next(false);

    // Restore original text in chat messages if text replacement was used
    this.restoreOriginalChatMessageText();

    try {
      // Process the wireframe response and create nodes
      if (this.isUIDesignResponse(response)) {
        this.processUIDesignResponse(response);
        this.logger.info('✅ UI Design nodes created successfully from API response');
      } else {
        this.logger.warn('⚠️ Invalid wireframe response format, showing error');
        this.showUIDesignError('Invalid response format from wireframe generation API');
      }
    } catch (error) {
      this.logger.error('❌ Error processing wireframe response:', error);
      this.showUIDesignError('Failed to process wireframe generation response');
    }
  }

  /**
   * Handle failed UI Design API response
   */
  private handleUIDesignFailure(error: any): void {
    this.logger.error('🚨 Wireframe generation API failed:', error);

    // Update state
    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignLoading$.next(false);

    // Extract error message
    const errorMessage = error?.message || error?.error?.message || 'Wireframe generation failed';
    this.uiDesignError$.next(errorMessage);

    // Set error in wireframe generation state service
    this.wireframeGenerationStateService.setError(errorMessage);

    // Show error state
    this.showUIDesignError(errorMessage);
  }

  /**
   * Show UI Design error state
   */
  private showUIDesignError(message: string): void {
    this.logger.error('❌ UI Design error:', message);

    // Clear ALL loading nodes (comprehensive cleanup)
    this.clearAllLoadingNodes();

    // Clear editing and regeneration state if active
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);

    // Show error message in toast
    this.toastService.error(message);

    // Create error node
    const errorNode: UIDesignNode = {
      id: 'error-node',
      type: 'ui-design',
      data: {
        title: 'Error',
        htmlContent: this.sanitizer.bypassSecurityTrustHtml(`
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; padding: 20px; text-align: center;">
            <div style="font-size: 48px; color: #ef4444; margin-bottom: 16px;">⚠️</div>
            <div style="font-size: 16px; color: #374151; margin-bottom: 8px;">Generation Failed</div>
            <div style="font-size: 14px; color: #6b7280;">${message}</div>
          </div>
        `),
        rawContent: '',
        width: 400,
        height: 250,
        isLoading: false
      },
      position: { x: -200, y: -125 },
      selected: false,
      dragging: false,
      visible: true
    };

    this.uiDesignNodes$.next([errorNode]);
  }

  /**
   * Initialize UI Design canvas
   */
  private initializeUIDesignCanvas(): void {
    // Canvas will be initialized in ngAfterViewInit when ViewChild is available
    this.logger.info('🎯 UI Design canvas initialization scheduled');
  }

  /**
   * Load sample UI Design data for testing
   */
  private loadSampleUIDesignData(): void {
    // Create sample UI Design pages
    const samplePages: UIDesignPage[] = [
      {
        fileName: 'Landing Page',
        content: this.createSampleUIDesignHTML('Landing Page', 'Welcome to our amazing product', '#667eea')
      },
      {
        fileName: 'About Us',
        content: this.createSampleUIDesignHTML('About Us', 'Learn more about our company', '#764ba2')
      },
      {
        fileName: 'Services',
        content: this.createSampleUIDesignHTML('Services', 'Discover our premium services', '#4facfe')
      },
      {
        fileName: 'Contact',
        content: this.createSampleUIDesignHTML('Contact', 'Get in touch with us today', '#f093fb')
      }
    ];

    // Create UI Design nodes
    const nodes = this.uiDesignNodeService.createNodesFromPages(samplePages, this.sanitizer);
    this.uiDesignNodes$.next(nodes);

    // Enable UI Design mode and disable history view
    this.isUIDesignMode$.next(true);
    this.isHistoryActive$.next(false);

    this.logger.info('📱 Sample UI Design data loaded:', nodes.length, 'pages');
  }

  /**
   * Create sample HTML content for UI Design pages
   */
  private createSampleUIDesignHTML(title: string, description: string, primaryColor: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, ${primaryColor}20, ${primaryColor}10);
            min-height: 100vh;
            padding: 20px;
          }
          .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
          }
          .header {
            background: linear-gradient(135deg, ${primaryColor}, ${primaryColor}dd);
            color: white;
            padding: 40px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
          }
          .header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            font-weight: 700;
          }
          .header p {
            font-size: 1.1rem;
            opacity: 0.9;
          }
          .content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
          }
          .card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid ${primaryColor};
          }
          .card h3 {
            color: ${primaryColor};
            margin-bottom: 10px;
          }
          .btn {
            background: ${primaryColor};
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            transition: opacity 0.2s;
          }
          .btn:hover { opacity: 0.9; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${title}</h1>
            <p>${description}</p>
          </div>
          <div class="content">
            <div class="card">
              <h3>Feature 1</h3>
              <p>Amazing feature that will help your business grow and succeed in the modern market.</p>
            </div>
            <div class="card">
              <h3>Feature 2</h3>
              <p>Innovative solution designed to streamline your workflow and increase productivity.</p>
            </div>
            <div class="card">
              <h3>Feature 3</h3>
              <p>Cutting-edge technology that provides the best user experience for your customers.</p>
            </div>
          </div>
          <button class="btn">Get Started</button>
        </div>
      </body>
      </html>
    `;
  }

  // ===== UI DESIGN CANVAS METHODS =====

  /**
   * Handle UI Design node double-click for full-screen modal
   */
  onUIDesignNodeDoubleClick(node: UIDesignNode): void {
    this.selectedUIDesignNode$.next(node);
    this.isUIDesignFullScreenOpen$.next(true);

    // Set view mode based on user's application target selection, default to mobile
    const userApplicationTarget = this.generateUIDesignService.getApplicationTarget();
    this.uiDesignViewMode$.next(userApplicationTarget || 'mobile');

    this.logger.info('🎨 UI Design node opened in full-screen:', {
      title: node.data.title,
      viewMode: userApplicationTarget || 'mobile (default)'
    });
  }

  /**
   * Close UI Design full-screen modal
   */
  closeUIDesignFullScreen(): void {
    this.isUIDesignFullScreenOpen$.next(false);
    this.selectedUIDesignNode$.next(null);
    this.logger.info('🎨 UI Design full-screen modal closed');
  }

  /**
   * Switch UI Design view mode (mobile/web)
   */
  switchUIDesignViewMode(mode: 'mobile' | 'web'): void {
    this.uiDesignViewMode$.next(mode);
    this.logger.info('🎨 UI Design view mode switched to:', mode);
  }

  /**
   * Toggle fullscreen mode for UI Design modal
   */
  toggleUIDesignModalFullScreen(): void {
    const isCurrentlyFullScreen = this.isUIDesignModalFullScreen$.value;
    this.isUIDesignModalFullScreen$.next(!isCurrentlyFullScreen);
    this.logger.info('🎨 UI Design modal fullscreen toggled:', { fullscreen: !isCurrentlyFullScreen });
  }

  /**
   * Exit fullscreen mode for UI Design modal
   */
  exitUIDesignModalFullScreen(): void {
    this.isUIDesignModalFullScreen$.next(false);
    this.logger.info('🎨 UI Design modal fullscreen exited');
  }

  /**
   * Open UI Design preview in new tab
   */
  openUIDesignInNewTab(): void {
    const selectedNode = this.selectedUIDesignNode$.value;
    if (!selectedNode?.data?.rawContent) {
      this.logger.warn('🎨 No content available to open in new tab');
      return;
    }

    try {
      // Create a new window/tab
      const newWindow = window.open('', '_blank');

      if (newWindow) {
        // Write the HTML content to the new window
        newWindow.document.write(selectedNode.data.rawContent);
        newWindow.document.close();

        // Set the title for the new tab using formatted display title
        const title = selectedNode.data.displayTitle || selectedNode.data.title || 'UI Design Preview';
        newWindow.document.title = title;

        this.logger.info('🎨 UI Design opened in new tab:', {
          title: title,
          contentLength: selectedNode.data.rawContent.length
        });
      } else {
        // Popup blocked or failed to open
        this.logger.warn('🎨 Failed to open new tab - popup may be blocked');

        // Fallback: create a blob URL and open it
        this.openUIDesignWithBlobUrl(selectedNode.data.rawContent, selectedNode.data.displayTitle || selectedNode.data.title);
      }
    } catch (error) {
      this.logger.error('🎨 Error opening UI Design in new tab:', error);

      // Fallback to blob URL method
      this.openUIDesignWithBlobUrl(selectedNode.data.rawContent, selectedNode.data.displayTitle || selectedNode.data.title);
    }
  }

  /**
   * Fallback method to open UI Design using blob URL
   */
  private openUIDesignWithBlobUrl(content: string, title?: string): void {
    try {
      // Create a blob with the HTML content
      const blob = new Blob([content], { type: 'text/html' });
      const blobUrl = URL.createObjectURL(blob);

      // Open the blob URL in a new tab
      const newWindow = window.open(blobUrl, '_blank');

      if (newWindow) {
        // Clean up the blob URL after a delay
        setTimeout(() => {
          URL.revokeObjectURL(blobUrl);
        }, 1000);

        this.logger.info('🎨 UI Design opened via blob URL:', {
          title: title || 'UI Design Preview',
          blobUrl: blobUrl
        });
      } else {
        this.logger.error('🎨 Failed to open new tab - all methods failed');
      }
    } catch (error) {
      this.logger.error('🎨 Error creating blob URL:', error);
    }
  }

  /**
   * Process UI Design API response and create nodes
   * Handles both formats:
   * - UI Design: [{"pageName": "...", "content": "<html>..."}]
   * - Wireframe: [{"fileName": "Login_Page.html", "content": "<html>..."}]
   */
  processUIDesignResponse(response: string | UIDesignAPIResponse[] | WireframeAPIResponse[]): void {
    this.logger.info('🎨 Processing UI Design/Wireframe API response:', typeof response);

    // 🧹 CRITICAL FIX: Clear all loading nodes immediately when processing response
    // This handles both initial generation and regeneration loading nodes
    this.clearAllLoadingNodes();

    try {
      let pages: any[] = [];

      // Parse response if it's a string
      if (typeof response === 'string') {
        try {
          pages = JSON.parse(response);
        } catch (parseError) {
          this.logger.error('🎨 Failed to parse API response string:', parseError);
          this.showUIDesignError('Failed to parse response data');
          return;
        }
      } else if (Array.isArray(response)) {
        pages = response;
      } else {
        this.logger.error('🎨 Invalid API response format:', response);
        this.showUIDesignError('Invalid response format');
        return;
      }

      // Validate response structure
      if (!Array.isArray(pages) || pages.length === 0) {
        this.logger.error('🎨 Empty or invalid pages array:', pages);
        this.showUIDesignError('No pages found in response');
        return;
      }

      // Convert API response to UI Design page data
      const uiDesignPages: UIDesignPageData[] = pages.map((page, index) => {
        let pageName: string;
        let content: string;

        // Handle different response formats
        if ('fileName' in page) {
          // Wireframe format: {"fileName": "Login_Page.html", "content": "..."}
          pageName = this.extractPageNameFromFileName(page.fileName);
          content = page.content;
          this.logger.info('🔧 Processing wireframe page:', {
            originalFileName: page.fileName,
            extractedPageName: pageName,
            contentLength: content?.length || 0
          });
        } else if ('pageName' in page) {
          // UI Design format: {"pageName": "...", "content": "..."}
          pageName = page.pageName?.trim() || `Page ${index + 1}`;
          content = page.content;
          this.logger.info('🎨 Processing UI design page:', {
            pageName: pageName,
            contentLength: content?.length || 0
          });
        } else {
          // Fallback for unknown format
          this.logger.warn('🎨 Unknown page format, using fallback:', page);
          pageName = `Page ${index + 1}`;
          content = page.content || '<html><body><p>No content available</p></body></html>';
        }

        return {
          fileName: pageName,
          content: content || '<html><body><p>No content available</p></body></html>'
        };
      });

      this.logger.info('🎨 Converted to UI Design pages:', {
        count: uiDesignPages.length,
        pages: uiDesignPages.map(p => ({ name: p.fileName, contentLength: p.content.length }))
      });

      // Create UI Design nodes from pages
      this.createUIDesignNodes(uiDesignPages);

    } catch (error) {
      this.logger.error('🎨 Error processing API response:', error);
      this.showUIDesignError('Failed to process response data');
    }
  }

  /**
   * Extract clean page name from fileName using UI Design filename transformation
   * ENHANCED: Now uses dedicated UI Design transformer with "Page" suffix
   * Examples:
   * - "login.html" → "Login Page"
   * - "dashboard.html" → "Dashboard Page"
   * - "user-profile.html" → "User Profile Page"
   * - "loginPage.html" → "Login Page Page" (handled by duplicate detection)
   * - "user_settings.html" → "User Settings Page"
   */
  private extractPageNameFromFileName(fileName: string): string {
    if (!fileName) {
      return 'Untitled Page';
    }

    try {
      // ENHANCED: Use the dedicated UI Design filename transformer service
      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(fileName);

      // Return the display title which includes the "Page" suffix
      const displayTitle = transformResult.displayTitle || 'Untitled Page';

      this.logger.info('📝 Extracted page name using UI Design transformer:', {
        originalFileName: fileName,
        displayTitle: displayTitle,
        canonicalKey: transformResult.canonicalKey,
        confidence: transformResult.confidence,
        transformationSteps: transformResult.transformationSteps
      });

      return displayTitle;
    } catch (error) {
      this.logger.error('📝 Error extracting page name from fileName:', fileName, error);
      return 'Untitled Page';
    }
  }

  /**
   * Create UI Design nodes from page data and display in canvas
   * Enhanced with robust filename normalization and intelligent node management
   */
  private async createUIDesignNodes(pages: UIDesignPageData[]): Promise<void> {
    this.logger.info('🎨 Creating UI Design nodes from pages with robust filename normalization:', pages.length);

    try {
      // 🧹 SAFEGUARD: Clear all loading nodes before creating new nodes
      // This ensures no loading nodes persist when new content is created
      this.clearAllLoadingNodes();

      // Get current nodes for intelligent update/create decisions
      const currentNodes = this.uiDesignNodes$.value;

      // Convert pages to wireframe page data format for the node management service
      const wireframePages = pages.map(page => ({
        fileName: page.fileName,
        content: page.content,
        pageName: page.fileName // Alternative field name for compatibility
      }));

      // Create position calculator function for new nodes
      const positionCalculator = (count: number, _existingNodes: any[]) => {
        const positioningResult = this.uiDesignNodePositioningService.calculateInitialGenerationPositions(count);
        return positioningResult.positions;
      };

      // Use the robust node management service to process the regeneration
      const result = await this.wireframeNodeManagementService.processRegenerationResponse(
        wireframePages,
        currentNodes,
        positionCalculator
      );

      this.logger.info('🎯 Node management result:', {
        totalProcessed: result.summary.totalProcessed,
        created: result.summary.created,
        updated: result.summary.updated,
        failed: result.summary.failed
      });

      // Update the nodes with the processed result
      this.uiDesignNodes$.next(result.updatedNodes);

      // Enable UI Design mode and disable history view
      this.isUIDesignMode$.next(true);
      this.isHistoryActive$.next(false);

      // Calculate optimal viewport position to show all nodes
      if (result.updatedNodes.length > 0) {
        const nodePositions = result.updatedNodes.map(node => node.position);
        const optimalViewport = this.uiDesignNodePositioningService.calculateOptimalViewport(nodePositions);

        // Auto-center the viewport to show all nodes with optimal zoom
        setTimeout(() => {
          this.centerCanvasOnNodesWithViewport(optimalViewport);
        }, 100);
      }

      this.logger.info('🎨 UI Design nodes processed successfully:', {
        nodeCount: result.updatedNodes.length,
        nodeIds: result.updatedNodes.map(n => n.id),
        summary: result.summary
      });

      // ===== Trigger Overview Tab =====
      // Convert UIDesignPageData to MobilePage format for overview tab
      const mobilePages: MobilePage[] = pages.map(page => ({
        fileName: page.fileName,
        content: page.content
      }));

      // Set the UI Design response data in the service to trigger overview tab
      const responseData = {
        pages: mobilePages,
        jobId: 'ui-design-' + Date.now(), // Generate a temporary job ID
        projectId: 'ui-design-project-' + Date.now() // Generate a temporary project ID
      };

      this.logger.info('📱 Setting UI Design response data to trigger overview tab:', responseData);
      this.generateUIDesignService.setUIDesignResponse(responseData);

      // Update chat messages with success (enhanced with operation details)
      this.updateUIDesignChatMessagesWithSummary(result.summary);

      // Set nodes created state to show tooltip
      this.uiDesignSelectionService.setNodesCreated(true);
      this.logger.info('✅ Nodes created state set, tooltip will be shown');

      // Set wireframe generation complete state to enable selection controls
      this.isWireframeGenerationComplete$.next(true);
      this.wireframeGenerationStateService.completeGeneration(result.updatedNodes.length);
      this.logger.info('✅ Wireframe generation complete state set, selection controls enabled');

    } catch (error) {
      this.logger.error('🎨 Error creating UI Design nodes:', error);
      this.showUIDesignError('Failed to create design nodes: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }

  /**
   * Update chat messages for successful UI Design generation
   * Enhanced with AI message preservation and typewriting effects
   */
  private updateUIDesignChatMessages(pageCount: number): void {
    // CRITICAL: DO NOT remove AI messages - only remove specific loading messages
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage = msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage = msg.from === 'ai' && msg.id && (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      // Keep all AI messages, remove only temporary loading messages
      return !isTemporaryLoadingMessage || isAIMessage;
    });

    // Add success message with typewriting effect
    const successMessage = `✅ Successfully generated ${pageCount} UI design page${pageCount > 1 ? 's' : ''}! Single-click to select a page for editing, or double-click to view in full-screen mode.`;
    const messageId = `ai-success-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    this.lightMessages.push({
      id: messageId,
      text: '', // Start with empty text for typewriting effect
      from: 'ai',
      theme: 'light'
    });

    // Start typewriting effect for the success message
    this.startTypewriterEffectForMessage(successMessage, messageId);

    this.cdr.markForCheck();
  }

  /**
   * Start typewriter effect for a specific message
   * Ensures cards maintain consistent size while only text has typewriting effect
   */
  private startTypewriterEffectForMessage(text: string, messageId: string): void {
    const typingSpeed = 30; // milliseconds per character
    let charIndex = 0;

    const typeCharacter = () => {
      if (charIndex >= text.length) {
        return; // Typing complete
      }

      // Find the message by ID
      const message = this.lightMessages.find(msg => msg.id === messageId);
      if (!message) {
        return; // Message not found
      }

      // Update text with current progress
      const currentText = text.substring(0, charIndex + 1);
      message.text = currentText;

      charIndex++;

      // Schedule next character
      setTimeout(typeCharacter, typingSpeed);

      // Trigger change detection
      this.cdr.markForCheck();
    };

    // Start typing
    typeCharacter();
  }

  /**
   * Update chat messages with enhanced operation summary
   * Enhanced with AI message preservation
   */
  private updateUIDesignChatMessagesWithSummary(summary: any): void {
    // CRITICAL: DO NOT remove AI messages - only remove specific loading messages
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryLoadingMessage = msg.text.includes('Generating') || msg.text.includes('Processing');
      const isAIMessage = msg.from === 'ai' && msg.id && (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      // Keep all AI messages, remove only temporary loading messages
      return !isTemporaryLoadingMessage || isAIMessage;
    });

    // Create detailed success message based on operations performed
    let message = '✅ Wireframe processing completed successfully! ';

    if (summary.created > 0 && summary.updated > 0) {
      message += `Created ${summary.created} new page${summary.created > 1 ? 's' : ''} and updated ${summary.updated} existing page${summary.updated > 1 ? 's' : ''}.`;
    } else if (summary.created > 0) {
      message += `Created ${summary.created} new page${summary.created > 1 ? 's' : ''}.`;
    } else if (summary.updated > 0) {
      message += `Updated ${summary.updated} existing page${summary.updated > 1 ? 's' : ''}.`;
    } else {
      message += `Processed ${summary.totalProcessed} page${summary.totalProcessed > 1 ? 's' : ''}.`;
    }

    message += ' Single-click to select a page for editing, or double-click to view in full-screen mode.';

    // Add success message with typewriting effect
    const messageId = `ai-success-summary-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    this.lightMessages.push({
      id: messageId,
      text: '', // Start with empty text for typewriting effect
      from: 'ai',
      theme: 'light'
    });

    // Start typewriting effect for the success message
    this.startTypewriterEffectForMessage(message, messageId);

    this.cdr.markForCheck();
  }



  /**
   * Process UI Design response from polling or direct API call
   * This method serves as the main entry point for UI Design response processing
   */
  handleUIDesignResponse(response: any): void {
    this.logger.info('🎨 Handling UI Design response:', response);

    // Check if this is a UI Design response
    if (this.isUIDesignResponse(response)) {
      this.processUIDesignResponse(response);
    } else {
      this.logger.warn('🎨 Response is not a valid UI Design response:', response);
    }
  }

  /**
   * Check if response is a valid UI Design or Wireframe response
   */
  private isUIDesignResponse(response: any): boolean {
    // Check for array format
    if (Array.isArray(response)) {
      return response.every(item => {
        if (typeof item !== 'object' || !item.content) {
          return false;
        }

        // Check for UI Design format: {"pageName": "...", "content": "..."}
        const hasPageName = 'pageName' in item;

        // Check for Wireframe format: {"fileName": "...", "content": "..."}
        const hasFileName = 'fileName' in item;

        // Must have either pageName or fileName
        return hasPageName || hasFileName;
      });
    }

    // Check for string format that can be parsed
    if (typeof response === 'string') {
      try {
        const parsed = JSON.parse(response);
        return this.isUIDesignResponse(parsed);
      } catch {
        return false;
      }
    }

    return false;
  }

  /**
   * Check if artifact data contains UI Design response
   */
  private isUIDesignArtifact(artifactData: any): boolean {
    // Check if artifact contains UI Design data
    if (!artifactData || !artifactData.content) {
      return false;
    }

    // Check if the content is a UI Design response
    return this.isUIDesignResponse(artifactData.content);
  }

  /**
   * Handle canvas mouse down event
   */
  onCanvasMouseDown(event: MouseEvent): void {
    if (event.button === 0) { // Left mouse button
      this.uiDesignCanvasService.startDragging(event.clientX, event.clientY);
    }
  }

  /**
   * Handle canvas mouse move event
   */
  onCanvasMouseMove(event: MouseEvent): void {
    const viewport = this.uiDesignCanvasService.getViewport();

    if (viewport.isDragging) {
      const deltaX = event.clientX - viewport.lastMouseX;
      const deltaY = event.clientY - viewport.lastMouseY;

      this.uiDesignCanvasService.panCanvas(deltaX, deltaY);
      this.uiDesignCanvasService.updateViewport({
        lastMouseX: event.clientX,
        lastMouseY: event.clientY
      });
    }
  }

  /**
   * Handle canvas mouse up event
   */
  onCanvasMouseUp(_event: MouseEvent): void {
    this.uiDesignCanvasService.stopDragging();
  }

  /**
   * Handle node selection for editing (enhanced with multi-selection support)
   */
  onUIDesignNodeSelect(node: UIDesignNode, event?: MouseEvent): void {
    if (!this.isUIDesignMode$.value) {
      return;
    }

    // Determine if this is a multi-select operation
    const isMultiSelect = event && (event.ctrlKey || event.metaKey || event.shiftKey);

    this.logger.info('🎯 UI Design node selected for editing (multi-selection):', {
      nodeId: node.id,
      title: node.data.title,
      hasContent: !!node.data.rawContent,
      position: node.position,
      dimensions: { width: node.data.width, height: node.data.height },
      isMultiSelect: isMultiSelect
    });

    // Create enhanced selection data with metadata
    const selectionData: MultiSelectedNodeData = {
      nodeId: node.id,
      fileName: node.data.displayTitle || node.data.title, // Use formatted display title for API
      htmlContent: node.data.htmlContent as string,
      rawContent: node.data.rawContent,
      selectedImages: [], // Initialize empty, can be populated later
      metadata: {
        nodePosition: node.position,
        nodeDimensions: { width: node.data.width, height: node.data.height },
        selectionTimestamp: Date.now()
      }
    };

    // Update selection service with multi-selection support
    this.uiDesignSelectionService.selectMultipleNodes(node.id, selectionData, isMultiSelect || false);

    // Update visual feedback service for enhanced selection visibility
    this.uiDesignVisualFeedbackService.toggleNodeSelection(node.id, isMultiSelect || false);

    this.logger.info('✅ Node selection completed, prompt bar enabled, tooltip hidden');
  }

  /**
   * Update visual selection state for nodes (enhanced for multi-selection)
   * Now only triggers change detection since selection state is managed by the service
   */
  private updateNodeSelectionVisuals(): void {
    // Selection state is now managed entirely by the uiDesignSelectionService
    // and accessed via isNodeSelectedForEditing() method in the template
    // We only need to trigger change detection to update the UI
    this.cdr.markForCheck();

    this.logger.info('🎯 Visual selection state updated via change detection');
  }

  /**
   * Clear node selection
   */
  onClearNodeSelection(): void {
    this.logger.info('🔄 Clearing node selection');

    this.uiDesignSelectionService.clearSelection();

    // Clear visual feedback service selection
    this.uiDesignVisualFeedbackService.clearSelection();

    // Clear visual selection
    const currentNodes = this.uiDesignNodes$.value;
    const updatedNodes = currentNodes.map(node => ({
      ...node,
      selected: false
    }));

    this.uiDesignNodes$.next(updatedNodes);
    this.cdr.markForCheck();

    this.logger.info('✅ Node selection cleared');
  }

  /**
   * Generate unique regeneration session ID
   */
  private generateRegenerationSessionId(): string {
    this.regenerationSessionCounter++;
    return `regen-session-${Date.now()}-${this.regenerationSessionCounter}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Handle edit prompt submission for selected nodes (enhanced for multi-selection with loading nodes)
   * Ensures clean message state without legacy messages and shows loading nodes immediately
   */
  onUIDesignEditPrompt(prompt: string): void {
    if (!this.isUIDesignMode$.value) {
      this.logger.warn('⚠️ Edit prompt submitted but not in UI Design mode');
      return;
    }

    const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
    if (selectedNodes.length === 0) {
      this.logger.warn('⚠️ Edit prompt submitted but no nodes selected');
      this.toastService.error('Please select one or more pages to edit');
      return;
    }

    this.logger.info('🔧 Starting UI Design edit process with loading nodes (multi-selection):', {
      selectedCount: selectedNodes.length,
      nodeIds: selectedNodes.map(node => node.nodeId),
      fileNames: selectedNodes.map(node => node.fileName),
      prompt: prompt
    });

    // Clean up any legacy messages before starting edit process
    this.cleanupLegacyEditMessages();

    // Create and show loading nodes immediately
    this.createLoadingNodesForRegeneration(selectedNodes);

    // Set editing and regeneration state
    this.uiDesignSelectionService.setEditingInProgress(true);
    this.isUIDesignRegenerating$.next(true);
    this.isUIDesignLoading$.next(true);
    this.logger.info('🚫 UI Design regeneration started with loading nodes - prompt bar disabled');

    // ENHANCED: Generate unique session ID for this regeneration
    const sessionId = this.generateRegenerationSessionId();

    // ENHANCED: Always add user message for each regeneration (creates separate conversation threads)
    this.lightMessages.push({
      text: prompt,
      from: 'user',
      theme: 'light'
    });
    this.logger.info('📝 Added user regeneration message to chat with session ID:', sessionId);

    // Generate unique AI message ID for this specific regeneration
    const aiMessageId = `ai-regeneration-${sessionId}`;

    // ENHANCED: Always create a new AI message for each regeneration (no duplicate prevention)
    // This ensures each regeneration gets its own separate message thread
    this.lightMessages.push({
      id: aiMessageId,
      text: '', // Start with empty text - will be populated by intro API
      from: 'ai' as const,
      theme: 'light' as const,
      showIntroMessage: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      mainAPIInProgress: true
    });

    // Track this regeneration session
    this.activeRegenerationSessions.set(sessionId, {
      sessionId,
      messageId: aiMessageId,
      timestamp: Date.now(),
      prompt,
      selectedNodes: [...selectedNodes]
    });

    // Track the AI message for lifecycle management
    this.activeAIMessageIds.add(aiMessageId);
    this.currentActiveMessageId = aiMessageId;

    this.logger.info('📝 Created NEW AI regeneration message with unique ID:', aiMessageId);
    this.logger.info('📝 Regeneration session ID:', sessionId);
    this.logger.info('📝 AI regeneration message will be populated by intro API response');
    this.logger.info('📝 Total messages in lightMessages:', this.lightMessages.length);
    this.logger.info('📝 Active regeneration sessions:', this.activeRegenerationSessions.size);

    // Build edit request (now supports multi-selection)
    const editRequest = this.uiDesignSelectionService.buildEditRequest(prompt);
    if (!editRequest) {
      this.handleEditFailure('Failed to build edit request');
      return;
    }

    // Create the main edit API call observable
    const mainEditAPICall = this.uiDesignEditService.editUIDesignPage(editRequest);

    // Execute parallel API calls (main edit + intro) for regeneration with text replacement
    this.uiDesignIntroService.executeParallelRegeneration(prompt, selectedNodes, mainEditAPICall, this.currentActiveMessageId || undefined)
      .subscribe({
        next: (result) => {
          this.logger.info('✅ Parallel regeneration API calls completed:', result);

          if (result.mainAPISuccess) {
            this.handleEditSuccess(result.mainAPIResult, selectedNodes);
            // Complete text replacement when main API succeeds
            this.uiDesignIntroService.completeTextReplacement();
          } else {
            this.handleEditFailure('Main edit API failed');
          }
        },
        error: (error) => {
          this.handleEditFailure(error.message || 'Parallel regeneration API calls failed');
        }
      });

    this.cdr.markForCheck();
  }

  /**
   * Create loading nodes for regeneration operations
   */
  private createLoadingNodesForRegeneration(selectedNodes: MultiSelectedNodeData[]): void {
    this.logger.info('🔄 Creating loading nodes for regeneration:', {
      selectedCount: selectedNodes.length,
      nodeIds: selectedNodes.map(node => node.nodeId)
    });

    const currentNodes = this.uiDesignNodes$.value;
    const loadingNodes: UIDesignNode[] = [];

    // Generate context-specific loading message
    const loadingMessage = this.generateLoadingMessage(selectedNodes);

    selectedNodes.forEach((selectedNode, index) => {
      // Find the existing node to get its position and dimensions
      const existingNode = currentNodes.find(node => node.id === selectedNode.nodeId);
      if (!existingNode) {
        this.logger.warn('⚠️ Could not find existing node for loading node creation:', selectedNode.nodeId);
        return;
      }

      // Create loading node with same position and dimensions as original
      const loadingNode: UIDesignNode = {
        id: `loading-${selectedNode.nodeId}-${Date.now()}-${index}`,
        type: 'ui-design',
        data: {
          title: selectedNode.fileName,
          displayTitle: selectedNode.fileName,
          htmlContent: '',
          rawContent: '',
          width: existingNode.data.width,
          height: existingNode.data.height,
          isLoading: true,
          loadingMessage: loadingMessage,
          originalNodeId: selectedNode.nodeId // Track which node this is loading for
        },
        position: { ...existingNode.position }, // Copy position
        selected: false,
        dragging: false,
        visible: true
      };

      loadingNodes.push(loadingNode);
    });

    // Update loading nodes state
    this.uiDesignLoadingNodes$.next(loadingNodes);

    this.logger.info('✅ Loading nodes created and displayed:', {
      loadingNodesCount: loadingNodes.length,
      loadingMessage
    });
  }

  /**
   * Generate context-specific loading message based on selected nodes
   */
  private generateLoadingMessage(selectedNodes: MultiSelectedNodeData[]): string {
    if (selectedNodes.length === 1) {
      return `Editing ${selectedNodes[0].fileName}...`;
    } else {
      return `Editing ${selectedNodes.length} selected pages...`;
    }
  }

  /**
   * Clear loading nodes when regeneration completes
   */
  private clearLoadingNodes(): void {
    this.logger.info('🧹 Clearing regeneration loading nodes');
    this.uiDesignLoadingNodes$.next([]);
  }

  /**
   * Clear all types of loading nodes (comprehensive cleanup)
   * Handles both initial generation and regeneration loading nodes
   */
  private clearAllLoadingNodes(): void {
    this.logger.info('🧹 Clearing ALL loading nodes (initial generation + regeneration)');

    // Clear regeneration loading nodes (separate BehaviorSubject)
    this.uiDesignLoadingNodes$.next([]);

    // Clear any loading nodes that might be in the main nodes array
    // This handles initial generation loading nodes
    const currentNodes = this.uiDesignNodes$.value;
    const nonLoadingNodes = currentNodes.filter(node =>
      !node.data.isLoading &&
      !node.id.startsWith('loading-') &&
      node.id !== 'ui-design-loading-node'
    );

    // Only update if we actually removed loading nodes
    if (nonLoadingNodes.length !== currentNodes.length) {
      this.logger.info('🧹 Removed loading nodes from main nodes array:', {
        originalCount: currentNodes.length,
        filteredCount: nonLoadingNodes.length,
        removedCount: currentNodes.length - nonLoadingNodes.length
      });
      this.uiDesignNodes$.next(nonLoadingNodes);
    }

    this.logger.info('✅ All loading nodes cleared successfully');
  }

  /**
   * Handle successful edit response (enhanced for multi-selection with loading node cleanup)
   */
  private handleEditSuccess(response: any, selectedNodes: MultiSelectedNodeData[]): void {
    this.logger.info('✅ Edit API response received for multi-selection:', response);

    // Clear loading nodes first
    this.clearLoadingNodes();

    // Validate response
    const validatedResponse = this.uiDesignSelectionService.validateEditResponse(response);
    if (!validatedResponse) {
      this.handleEditFailure('Invalid response format from edit API');
      return;
    }

    // Update multiple nodes based on response
    this.updateMultipleNodesContent(validatedResponse, selectedNodes);

    // Update chat messages
    const fileNames = selectedNodes.map(node => node.fileName);
    this.updateEditChatMessages(true, fileNames);

    // Restore original text in chat messages if text replacement was used
    this.restoreOriginalChatMessageText();

    // Clear editing and regeneration state
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);
    this.logger.info('✅ UI Design regeneration completed - prompt bar re-enabled');

    this.logger.info('✅ Multi-selection edit process completed successfully');
  }

  /**
   * Handle edit failure (enhanced with comprehensive loading node cleanup)
   */
  private handleEditFailure(errorMessage: string): void {
    this.logger.error('❌ Edit process failed:', errorMessage);

    // Clear ALL loading nodes (comprehensive cleanup)
    this.clearAllLoadingNodes();

    // Update chat messages
    this.updateEditChatMessages(false, '', errorMessage);

    // Clear editing and regeneration state
    this.uiDesignSelectionService.setEditingInProgress(false);
    this.isUIDesignRegenerating$.next(false);
    this.isUIDesignLoading$.next(false);
    this.logger.info('❌ UI Design regeneration failed - prompt bar re-enabled');

    // Show error toast
    this.toastService.error(errorMessage);

    this.cdr.markForCheck();
  }

  /**
   * Identify truly new files that don't already exist in the canvas
   * Enhanced with robust filename normalization service
   */
  public identifyTrulyNewFiles(
    currentNodes: UIDesignNode[],
    responseMap: Map<string, string>,
    selectedNodes: MultiSelectedNodeData[]
  ): {
    newFileNames: string[];
    existingFileNames: string[];
    responseFileNames: string[];
    duplicatesFiltered: string[];
    analysisDetails: any;
  } {
    this.logger.info('🔍 Starting robust duplicate detection analysis with filename normalization:', {
      currentNodesCount: currentNodes.length,
      responseFilesCount: responseMap.size,
      selectedNodesCount: selectedNodes.length
    });

    // Get all response file names
    const responseFileNames = Array.from(responseMap.keys());

    // Create simplified node data for the normalization service
    const existingNodeData = currentNodes.map(node => ({
      id: node.id,
      title: node.data.title,
      displayTitle: node.data.displayTitle
    }));

    // Analyze each response file using the robust normalization service
    const analysisResults = responseFileNames.map(fileName => {
      // Check if this is a file from a selected node (these should update, not create new)
      const isSelectedNodeFile = selectedNodes.some(selected =>
        selected.fileName === fileName ||
        this.filenameNormalizationService.normalizeFilename(selected.fileName).canonicalKey ===
        this.filenameNormalizationService.normalizeFilename(fileName).canonicalKey
      );

      if (isSelectedNodeFile) {
        return {
          fileName,
          isDuplicate: true,
          reason: 'File belongs to selected node - should update existing node',
          isSelectedNodeFile: true
        };
      }

      // Use the robust filename normalization service to find matches
      const matchResult = this.filenameNormalizationService.findMatchingNode(fileName, existingNodeData);

      return {
        fileName,
        isDuplicate: matchResult.isMatch,
        reason: matchResult.reason,
        matchType: matchResult.matchType,
        confidence: matchResult.confidence,
        matchedNodeId: matchResult.matchedNodeId,
        isSelectedNodeFile: false
      };
    });

    // Filter out duplicates and get truly new files
    const newFileNames = analysisResults
      .filter(result => !result.isDuplicate)
      .map(result => result.fileName);

    const duplicatesFiltered = analysisResults
      .filter(result => result.isDuplicate)
      .map(result => result.fileName);

    const result = {
      newFileNames,
      existingFileNames: existingNodeData.map(node => node.title),
      responseFileNames,
      duplicatesFiltered,
      analysisDetails: {
        totalAnalyzed: responseFileNames.length,
        newFilesFound: newFileNames.length,
        duplicatesFiltered: duplicatesFiltered.length,
        individualAnalysis: analysisResults,
        normalizationServiceUsed: true
      }
    };

    this.logger.info('✅ Robust duplicate detection analysis completed:', {
      totalResponseFiles: responseFileNames.length,
      newFilesIdentified: newFileNames.length,
      duplicatesFiltered: duplicatesFiltered.length,
      newFiles: newFileNames,
      duplicates: duplicatesFiltered,
      analysisDetails: result.analysisDetails.individualAnalysis
    });

    return result;
  }

  /**
   * Create comprehensive sets of existing file identifiers for duplicate detection
   */
  private createExistingFileIdentifierSets(currentNodes: UIDesignNode[]): {
    originalTitles: string[];
    formattedTitles: string[];
    normalizedTitles: string[];
    baseNames: string[];
    allIdentifiers: Set<string>;
  } {
    const originalTitles = currentNodes.map(node => node.data.title);
    const formattedTitles = originalTitles.map(title => this.formatFileName(title));
    const normalizedTitles = originalTitles.map(title => this.normalizeFileName(title));
    const baseNames = originalTitles.map(title => this.extractBaseName(title));

    // Create a comprehensive set of all possible identifiers
    const allIdentifiers = new Set<string>([
      ...originalTitles,
      ...formattedTitles,
      ...normalizedTitles,
      ...baseNames,
      // Add lowercase versions for case-insensitive matching
      ...originalTitles.map(title => title.toLowerCase()),
      ...formattedTitles.map(title => title.toLowerCase()),
      ...normalizedTitles.map(title => title.toLowerCase()),
      ...baseNames.map(title => title.toLowerCase())
    ]);

    return {
      originalTitles,
      formattedTitles,
      normalizedTitles,
      baseNames,
      allIdentifiers
    };
  }

  /**
   * Analyze a single file for potential duplicates
   */
  private analyzeFileForDuplicates(
    fileName: string,
    existingIdentifiers: any,
    selectedNodes: MultiSelectedNodeData[]
  ): {
    isDuplicate: boolean;
    reason: string;
    matchedIdentifier?: string;
    isSelectedNodeFile: boolean;
  } {
    // Check if this is a file from a selected node (these should update, not create new)
    const isSelectedNodeFile = selectedNodes.some(selected =>
      selected.fileName === fileName ||
      this.normalizeFileName(selected.fileName) === this.normalizeFileName(fileName)
    );

    if (isSelectedNodeFile) {
      return {
        isDuplicate: true,
        reason: 'File belongs to selected node - should update existing node',
        isSelectedNodeFile: true
      };
    }

    // Generate all possible identifiers for this file
    const fileIdentifiers = [
      fileName,
      this.formatFileName(fileName),
      this.normalizeFileName(fileName),
      this.extractBaseName(fileName),
      fileName.toLowerCase(),
      this.formatFileName(fileName).toLowerCase(),
      this.normalizeFileName(fileName).toLowerCase(),
      this.extractBaseName(fileName).toLowerCase()
    ];

    // Check for matches
    for (const identifier of fileIdentifiers) {
      if (existingIdentifiers.allIdentifiers.has(identifier)) {
        return {
          isDuplicate: true,
          reason: `Matches existing identifier: ${identifier}`,
          matchedIdentifier: identifier,
          isSelectedNodeFile: false
        };
      }
    }

    return {
      isDuplicate: false,
      reason: 'No duplicates found - truly new file',
      isSelectedNodeFile: false
    };
  }

  /**
   * Normalize file name for comparison (enhanced with robust normalization service)
   */
  private normalizeFileName(fileName: string): string {
    if (!fileName) return '';

    // Use the robust filename normalization service
    const result = this.filenameNormalizationService.normalizeFilename(fileName);
    return result.canonicalKey;
  }

  /**
   * Extract base name from file (remove path and extension)
   */
  private extractBaseName(fileName: string): string {
    if (!fileName) return '';

    // Remove path if present
    const nameOnly = fileName.split('/').pop() || fileName;

    // Remove extension
    return nameOnly.replace(/\.[^/.]+$/, '');
  }

  /**
   * Update multiple nodes content in canvas (enhanced for dynamic node creation)
   */
  private updateMultipleNodesContent(validatedResponse: any[], selectedNodes: MultiSelectedNodeData[]): void {
    const currentNodes = this.uiDesignNodes$.value;
    let updatedCount = 0;

    // Create a map of fileName to response content for quick lookup
    const responseMap = new Map<string, string>();
    validatedResponse.forEach(file => {
      responseMap.set(file.fileName, file.content);
    });

    // Enhanced duplicate detection to prevent adding existing nodes
    const duplicateAnalysis = this.identifyTrulyNewFiles(currentNodes, responseMap, selectedNodes);
    const newFileNames = duplicateAnalysis.newFileNames;

    this.logger.info('📊 Enhanced duplicate detection analysis:', {
      existingNodes: currentNodes.length,
      responsePages: duplicateAnalysis.responseFileNames.length,
      newPages: newFileNames.length,
      existingFileNames: duplicateAnalysis.existingFileNames,
      responseFileNames: duplicateAnalysis.responseFileNames,
      newFileNames,
      duplicatesFiltered: duplicateAnalysis.duplicatesFiltered,
      analysisDetails: duplicateAnalysis.analysisDetails
    });

    // Update existing nodes
    const updatedNodes = currentNodes.map(node => {
      // Check if this node was selected and has updated content
      const selectedNode = selectedNodes.find(selected => selected.nodeId === node.id);
      if (selectedNode && responseMap.has(selectedNode.fileName)) {
        const updatedContent = responseMap.get(selectedNode.fileName)!;

        // Enhance the updated HTML content
        const enhancedContent = this.enhanceHTMLWithCSS(updatedContent);
        const sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(enhancedContent);

        updatedCount++;
        return {
          ...node,
          data: {
            ...node.data,
            htmlContent: sanitizedContent,
            rawContent: enhancedContent
          }
        };
      }
      return node;
    });

    // Create new nodes for additional pages if any
    const newNodes = this.createNewNodesFromEditResponse(newFileNames, responseMap, updatedNodes.length);

    // Combine updated existing nodes with new nodes
    const finalNodes = [...updatedNodes, ...newNodes];

    this.uiDesignNodes$.next(finalNodes);
    this.cdr.markForCheck();

    this.logger.info('✅ Multiple nodes content updated with dynamic creation:', {
      totalSelected: selectedNodes.length,
      updatedCount: updatedCount,
      newNodesCreated: newNodes.length,
      responseCount: validatedResponse.length,
      finalNodeCount: finalNodes.length
    });

    // CRITICAL: Synchronize Overview Preview tab with updated canvas nodes
    this.synchronizeOverviewPreviewWithCanvasNodes(finalNodes, selectedNodes);
  }

  /**
   * Create new nodes from edit response for dynamic node creation
   * Enhanced with intelligent positioning using the positioning service
   */
  private createNewNodesFromEditResponse(
    newFileNames: string[],
    responseMap: Map<string, string>,
    existingNodeCount: number
  ): UIDesignNode[] {
    if (newFileNames.length === 0) {
      return [];
    }

    this.logger.info('🆕 Creating new nodes for additional pages with intelligent positioning:', {
      newFileNames,
      existingNodeCount,
      totalNewNodes: newFileNames.length
    });

    // Get existing nodes for positioning calculations
    const existingNodes = this.uiDesignNodes$.value.map(node => ({
      id: node.id,
      position: node.position,
      dimensions: { width: node.data.width, height: node.data.height }
    }));

    // Get selected node for intelligent positioning
    const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
    const selectedNodeId = selectedNodes.length > 0 ? selectedNodes[0].nodeId : '';

    // Calculate intelligent positions for new nodes using positioning service
    const positioningResult = this.uiDesignNodePositioningService.calculateRegenerationPositions(
      existingNodes,
      selectedNodeId,
      newFileNames.length
    );

    this.logger.info('📐 Intelligent positioning calculated for new nodes:', {
      strategy: positioningResult.strategy.type,
      description: positioningResult.strategy.description,
      newNodesCount: newFileNames.length,
      positions: positioningResult.positions
    });

    const newNodes: UIDesignNode[] = newFileNames.map((fileName, index) => {
      const content = responseMap.get(fileName) || '';

      // Enhance the HTML content
      const enhancedContent = this.enhanceHTMLWithCSS(content);
      const sanitizedContent = this.sanitizer.bypassSecurityTrustHtml(enhancedContent);

      // Use intelligent position from positioning service or fallback
      const position = positioningResult.positions[index] ||
        this.calculateNewNodePosition(existingNodeCount + index, existingNodeCount + newFileNames.length);

      const newNode: UIDesignNode = {
        id: this.generateNodeId(),
        type: 'ui-design',
        data: {
          title: fileName, // Keep original fileName for API compatibility
          displayTitle: this.formatFileName(fileName), // Formatted title for display
          htmlContent: sanitizedContent,
          rawContent: enhancedContent,
          width: 420, // Mobile device width
          height: 720, // Mobile device height
          isLoading: false
        },
        position: position,
        selected: false,
        dragging: false,
        visible: true
      };

      this.logger.info(`📱 Created new node for "${fileName}" with intelligent positioning:`, {
        nodeId: newNode.id,
        originalFileName: fileName,
        displayTitle: this.formatFileName(fileName),
        position: newNode.position,
        positioningStrategy: positioningResult.strategy.type,
        contentLength: enhancedContent.length
      });

      return newNode;
    });

    this.logger.info(`✅ Successfully created ${newNodes.length} new nodes with ${positioningResult.strategy.type} positioning strategy`);
    return newNodes;
  }

  /**
   * Calculate position for new nodes in the canvas grid
   */
  private calculateNewNodePosition(nodeIndex: number, totalNodes: number): { x: number; y: number } {
    const columnsPerRow = 2;
    const gridSpacing = { x: 480, y: 780 }; // Mobile node spacing

    const row = Math.floor(nodeIndex / columnsPerRow);
    const col = nodeIndex % columnsPerRow;

    // Center the grid
    const totalCols = Math.min(totalNodes, columnsPerRow);
    const gridWidth = totalCols * gridSpacing.x;
    const startX = -gridWidth / 2 + gridSpacing.x / 2;

    const totalRows = Math.ceil(totalNodes / columnsPerRow);
    const gridHeight = totalRows * gridSpacing.y;
    const startY = -gridHeight / 2 + gridSpacing.y / 2;

    return {
      x: startX + col * gridSpacing.x,
      y: startY + row * gridSpacing.y
    };
  }

  /**
   * Generate unique node ID for new nodes
   */
  private generateNodeId(): string {
    return `ui-design-node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Format file name for display using UI Design filename transformation
   * ENHANCED: Now uses dedicated UI Design transformer with "Page" suffix
   * Examples:
   * - "landing_page.html" → "Landing Page Page" (handled by duplicate detection)
   * - "dashboard.html" → "Dashboard Page"
   * - "user-profile.html" → "User Profile Page"
   */
  private formatFileName(fileName: string): string {
    if (!fileName) return '';

    try {
      // ENHANCED: Use the dedicated UI Design filename transformer service
      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(fileName);

      // Return the display title which includes the "Page" suffix
      const displayTitle = transformResult.displayTitle || 'Untitled Page';

      this.logger.info('📝 Formatted file name using UI Design transformer:', {
        originalFileName: fileName,
        displayTitle: displayTitle,
        confidence: transformResult.confidence
      });

      return displayTitle;
    } catch (error) {
      this.logger.error('📝 Error formatting file name:', fileName, error);

      // Fallback to basic transformation if service fails
      const nameWithoutExtension = fileName.replace(/\.[^/.]+$/, '');
      return nameWithoutExtension
        .replace(/[_-]/g, ' ')
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ')
        .trim() || 'Untitled Page';
    }
  }

  /**
   * Synchronize Overview Preview tab with updated canvas nodes
   * CRITICAL: This ensures the Overview Preview tab shows the same updated content as canvas nodes
   */
  private synchronizeOverviewPreviewWithCanvasNodes(updatedNodes: UIDesignNode[], selectedNodes: MultiSelectedNodeData[]): void {
    this.logger.info('🔄 Synchronizing Overview Preview tab with updated canvas nodes:', {
      updatedNodesCount: updatedNodes.length,
      selectedNodesCount: selectedNodes.length
    });

    try {
      // Get current UI Design response data
      const currentResponseData = this.generateUIDesignService.getUIDesignResponse();

      if (!currentResponseData) {
        this.logger.warn('⚠️ No UI Design response data found, creating new response data');
        this.createNewUIDesignResponseFromNodes(updatedNodes);
        return;
      }

      // Create updated pages array with new content from canvas nodes
      const updatedPages: MobilePage[] = updatedNodes.map(node => ({
        fileName: node.data.displayTitle || node.data.title, // Use formatted display title
        content: node.data.rawContent
      }));

      // Create new response data with updated pages
      const updatedResponseData: UIDesignResponseData = {
        ...currentResponseData,
        pages: updatedPages
      };

      // Update the UI Design service with new response data
      this.generateUIDesignService.setUIDesignResponse(updatedResponseData);

      // Update the local uiDesignPages observable
      this.uiDesignPages$.next(updatedPages);

      // Force change detection to ensure Overview Preview tab updates
      this.cdr.detectChanges();

      this.logger.info('✅ Overview Preview tab synchronized successfully:', {
        pagesCount: updatedPages.length,
        pageNames: updatedPages.map(p => p.fileName)
      });

    } catch (error) {
      this.logger.error('❌ Error synchronizing Overview Preview tab:', error);
    }
  }

  /**
   * Create new UI Design response data from canvas nodes
   */
  private createNewUIDesignResponseFromNodes(nodes: UIDesignNode[]): void {
    const pages: MobilePage[] = nodes.map(node => ({
      fileName: node.data.displayTitle || node.data.title, // Use formatted display title
      content: node.data.rawContent
    }));

    const responseData: UIDesignResponseData = {
      pages: pages,
      jobId: 'ui-design-' + Date.now(),
      projectId: 'ui-design-project-' + Date.now()
    };

    this.generateUIDesignService.setUIDesignResponse(responseData);
    this.uiDesignPages$.next(pages);

    this.logger.info('✅ Created new UI Design response data from canvas nodes:', {
      pagesCount: pages.length
    });
  }

  /**
   * Update selected node content in canvas (legacy single selection)
   */
  private updateSelectedNodeContent(enhancedContent: string, sanitizedContent: any): void {
    const selectedNode = this.uiDesignSelectionService.getSelectedNode();
    if (!selectedNode) {
      this.logger.warn('⚠️ Cannot update node content: No node selected');
      return;
    }

    const currentNodes = this.uiDesignNodes$.value;
    const updatedNodes = currentNodes.map(node => {
      if (node.id === selectedNode.nodeId) {
        return {
          ...node,
          data: {
            ...node.data,
            htmlContent: sanitizedContent,
            rawContent: enhancedContent
          }
        };
      }
      return node;
    });

    this.uiDesignNodes$.next(updatedNodes);
    this.cdr.markForCheck();

    // CRITICAL: Synchronize Overview Preview tab with updated canvas node (legacy single selection)
    this.synchronizeOverviewPreviewWithCanvasNodes(updatedNodes, []);

    this.logger.info('✅ Node content updated in canvas');
  }

  /**
   * Clean up any legacy edit messages that might be present
   * Removes outdated or problematic messages from previous implementations
   */
  private cleanupLegacyEditMessages(): void {
    this.logger.info('🧹 Cleaning up legacy edit messages');

    // Remove any legacy messages that might interfere with current edit workflow
    this.lightMessages = this.lightMessages.filter(msg => {
      const text = msg.text.toLowerCase();
      const isLegacyMessage =
        text.includes('hang tight') ||
        text.includes('working on edit') ||
        text.includes('edit is coming') ||
        text.includes('edit coming up') ||
        text.includes('we are working on') ||
        text.includes('processing your edit') ||
        text.includes('regenerating your') ||
        text.includes('updating your design') ||
        text.includes('modifying your page');

      if (isLegacyMessage) {
        this.logger.info('🗑️ Removed legacy message:', msg.text);
        return false;
      }
      return true;
    });

    this.cdr.markForCheck();
  }

  /**
   * Update chat messages for edit operations (enhanced for multi-selection)
   * Ensures clean message state and removes any legacy messages
   */
  private updateEditChatMessages(success: boolean, fileNames: string | string[], _errorMessage?: string): void {
    // CRITICAL: DO NOT remove AI messages - only remove specific loading/editing messages
    this.lightMessages = this.lightMessages.filter(msg => {
      const isTemporaryEditingMessage = msg.text.includes('editing') ||
        msg.text.includes('Please wait') ||
        msg.text.includes('Hang tight') ||
        msg.text.includes('working on edit') ||
        msg.text.includes('edit is coming') ||
        msg.text.toLowerCase().includes('processing') ||
        msg.text.toLowerCase().includes('regenerating');

      const isAIMessage = msg.from === 'ai' && msg.id && (msg.id.startsWith('ai-generation-') || msg.id.startsWith('ai-regeneration-'));

      // Keep all AI messages, remove only temporary editing messages
      return !isTemporaryEditingMessage || isAIMessage;
    });

    if (success) {
      // Handle both single and multiple file names
      let successMessage: string;
      if (Array.isArray(fileNames)) {
        if (fileNames.length === 1) {
          successMessage = `✅ Successfully updated "${fileNames[0]}"! The changes are now visible in the canvas.`;
        } else {
          successMessage = `✅ Successfully updated ${fileNames.length} pages! The changes are now visible in the canvas.`;
        }
      } else {
        successMessage = `✅ Successfully updated "${fileNames}"! The changes are now visible in the canvas.`;
      }

      // Add success message with typewriting effect
      const messageId = `ai-edit-success-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      this.lightMessages.push({
        id: messageId,
        text: '', // Start with empty text for typewriting effect
        from: 'ai',
        theme: 'light'
      });

      // Start typewriting effect for the success message
      this.startTypewriterEffectForMessage(successMessage, messageId);
    } else {
      // Add error message
      // this.lightMessages.push({
      //   text: `❌ Failed to update the page: ${errorMessage}. Please try again.`,
      //   from: 'ai',
      //   theme: 'light'
      // });
    }

    this.cdr.markForCheck();
  }

  /**
   * Enhance HTML content with CSS frameworks for proper iframe rendering
   * (Simplified version for code-window component)
   */
  private enhanceHTMLWithCSS(htmlContent: string): string {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '<html><body><p>No content available</p></body></html>';
    }

    // Check if HTML already has a complete document structure
    const hasDoctype = htmlContent.includes('<!DOCTYPE');
    const hasHtmlTag = htmlContent.includes('<html');
    const hasHead = htmlContent.includes('<head');

    if (hasDoctype && hasHtmlTag && hasHead) {
      // HTML is already complete, just ensure CSS frameworks are present
      return this.injectCSSFrameworksIfMissing(htmlContent);
    } else {
      // HTML is incomplete, wrap it in a complete document structure
      return this.wrapInCompleteHTML(htmlContent);
    }
  }

  /**
   * Inject CSS frameworks if missing from existing HTML
   */
  private injectCSSFrameworksIfMissing(htmlContent: string): string {
    let enhanced = htmlContent;

    // Check for Tailwind CSS
    if (!enhanced.includes('tailwindcss')) {
      enhanced = enhanced.replace(
        '</head>',
        '  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">\n</head>'
      );
    }

    // Check for Bootstrap
    if (!enhanced.includes('bootstrap')) {
      enhanced = enhanced.replace(
        '</head>',
        '  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">\n</head>'
      );
    }

    return enhanced;
  }

  /**
   * Wrap HTML fragment in complete document structure
   */
  private wrapInCompleteHTML(htmlContent: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>UI Design Preview</title>

  <!-- CSS Frameworks -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      line-height: 1.6 !important;
      margin: 0 !important;
      padding: 20px !important;
      background: #f8fafc !important;
      min-height: 100vh;
    }
    * { box-sizing: border-box !important; }
    .container { max-width: 100% !important; margin: 0 auto !important; }
    img { max-width: 100%; height: auto; }
  </style>
</head>
<body class="iframe-content">
  ${htmlContent}
</body>
</html>`;
  }

  /**
   * Check if a specific node is selected for editing
   * This is the single source of truth for node selection state
   */
  isNodeSelectedForEditing(nodeId: string): boolean {
    return this.uiDesignSelectionService.isNodeSelected(nodeId);
  }

  /**
   * Select all available nodes
   */
  selectAllNodes(): void {
    const currentNodes = this.uiDesignNodes$.value;
    if (currentNodes.length === 0) {
      return;
    }

    // Convert UI Design nodes to MultiSelectedNodeData
    const allNodesData: MultiSelectedNodeData[] = currentNodes.map(node => ({
      nodeId: node.id,
      fileName: node.data.displayTitle || node.data.title, // Use formatted display title for API
      htmlContent: node.data.htmlContent as string,
      rawContent: node.data.rawContent,
      selectedImages: [],
      metadata: {
        nodePosition: node.position,
        nodeDimensions: { width: node.data.width, height: node.data.height },
        selectionTimestamp: Date.now()
      }
    }));

    this.uiDesignSelectionService.selectAllNodes(allNodesData);
    this.logger.info('🎯 All nodes selected:', allNodesData.length);
  }

  /**
   * Clear all node selections
   */
  clearAllSelection(): void {
    this.uiDesignSelectionService.clearSelection();
    this.logger.info('🔄 All selections cleared');
  }

  /**
   * Get the count of selected nodes
   */
  getSelectedNodesCount(): number {
    return this.uiDesignSelectionService.getSelectedNodesCount();
  }

  /**
   * Get prompt bar placeholder text based on UI Design mode and selection state (enhanced for multi-selection)
   */
  getPromptBarPlaceholder(): string {
    if (this.isUIDesignMode$.value) {
      return this.uiDesignSelectionService.getSelectionSummary();
    }
    return 'Ask me';
  }

  /**
   * Get prompt bar enabled state based on UI Design mode, selection, and regeneration status
   */
  getPromptBarEnabledState(): boolean {
    if (this.isUIDesignMode$.value) {
      // In UI Design mode, disable prompt bar during regeneration
      if (this.isUIDesignRegenerating$.value) {
        return false;
      }
      return this.uiDesignSelectionService.getIsPromptBarEnabled();
    }
    return this.isPromptBarEnabled;
  }

  /**
   * Get prompt bar disabled state (inverse of enabled state)
   */
  getPromptBarDisabledState(): boolean {
    if (this.isUIDesignMode$.value) {
      // In UI Design mode, enable disabled state during regeneration
      if (this.isUIDesignRegenerating$.value) {
        return true;
      }
      return !this.uiDesignSelectionService.getIsPromptBarEnabled();
    }
    return !this.isPromptBarEnabled;
  }

  /**
   * Handle prompt submission for UI Design editing
   */
  handleUIDesignPromptSubmission(): void {
    if (this.isUIDesignMode$.value) {
      // Handle UI Design edit prompt
      if (this.lightPrompt && this.lightPrompt.trim()) {
        this.onUIDesignEditPrompt(this.lightPrompt.trim());
        this.lightPrompt = ''; // Clear the prompt after submission
      }
    } else {
      // Handle standard prompt submission
      this.handleEnhancedSendLight();
    }
  }

  /**
   * Handle canvas wheel event for zooming
   */
  onCanvasWheel(event: WheelEvent): void {
    event.preventDefault();

    const rect = this.uiDesignCanvas?.nativeElement.getBoundingClientRect();
    if (rect) {
      const centerPoint = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      };

      if (event.deltaY < 0) {
        this.uiDesignCanvasService.zoomIn(centerPoint);
      } else {
        this.uiDesignCanvasService.zoomOut(centerPoint);
      }
    }
  }

  /**
   * Zoom in canvas
   */
  zoomInCanvas(): void {
    this.uiDesignCanvasService.zoomIn();
  }

  /**
   * Zoom out canvas
   */
  zoomOutCanvas(): void {
    this.uiDesignCanvasService.zoomOut();
  }

  /**
   * Reset canvas view - centers nodes and resets zoom
   */
  resetCanvasView(): void {
    // Update container size first
    this.updateCanvasContainerSize();

    // Reset zoom to 1 and center nodes
    this.uiDesignCanvasService.resetViewport();

    // Center nodes after a brief delay to ensure viewport is reset
    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 50);

    this.logger.info('🔄 Canvas view reset and centered');
  }

  /**
   * Fit canvas content to view
   */
  fitCanvasToView(): void {
    this.uiDesignViewportService.fitContentToView();
  }

  /**
   * Center canvas on nodes
   */
  centerCanvasOnNodes(): void {
    this.uiDesignViewportService.centerViewOnNodes();
    this.logger.info('🎯 Canvas centered on UI Design nodes');
  }

  /**
   * Center canvas on nodes with optimal viewport settings
   * Enhanced for intelligent positioning using positioning service calculations
   */
  centerCanvasOnNodesWithViewport(viewport: { x: number; y: number; zoom: number }): void {
    this.logger.info('🎯 Centering canvas with optimal viewport:', viewport);

    // Update viewport with calculated optimal settings using canvas service
    this.uiDesignCanvasService.updateViewport({
      x: viewport.x,
      y: viewport.y,
      zoom: viewport.zoom
    });

    this.logger.info('✅ Canvas centered with optimal viewport settings:', {
      position: { x: viewport.x, y: viewport.y },
      zoom: viewport.zoom
    });
  }

  /**
   * Update canvas container size for proper viewport calculations
   * Calculates the exact usable space within the right panel's viewport
   */
  updateCanvasContainerSize(): void {
    if (this.uiDesignCanvas?.nativeElement) {
      const canvasContainer = this.uiDesignCanvas.nativeElement;
      const canvasRect = canvasContainer.getBoundingClientRect();

      // Get the right panel content area for more precise calculations
      const rightPanelContent = canvasContainer.closest('[awe-rightpanel-content]');
      const rightPanelRect = rightPanelContent?.getBoundingClientRect();

      // Use right panel dimensions if available, otherwise fall back to canvas container
      const containerRect = rightPanelRect || canvasRect;

      // Calculate precise available space accounting for UI elements
      const canvasControlsHeight = 60; // Height of canvas controls overlay
      const canvasControlsPadding = 20; // Padding around controls
      const rightPanelPadding = 16; // Right panel internal padding

      // Calculate usable dimensions within the right panel viewport
      const availableWidth = containerRect.width - (rightPanelPadding * 2);
      const availableHeight = containerRect.height - canvasControlsHeight - canvasControlsPadding - rightPanelPadding;

      // Ensure minimum usable dimensions
      const finalWidth = Math.max(availableWidth, 300);
      // const finalHeight = Math.max(availableHeight, 500);
      const finalHeight = 500;

      this.uiDesignViewportService.updateContainerSize(finalWidth, finalHeight);

      this.logger.info('📐 Right panel viewport size calculated:', {
        rightPanelDimensions: rightPanelRect ? {
          width: rightPanelRect.width,
          height: rightPanelRect.height
        } : 'not found',
        canvasDimensions: {
          width: canvasRect.width,
          height: canvasRect.height
        },
        availableSpace: {
          width: availableWidth,
          height: availableHeight
        },
        finalDimensions: {
          width: finalWidth,
          height: finalHeight
        }
      });
    }
  }

  /**
   * Setup auto-centering functionality for UI Design canvas
   */
  private setupAutoCanvasCentering(): void {
    // Initial container size update and centering
    this.updateCanvasContainerSize();

    // Center nodes after container size is updated
    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 100);

    // Setup ResizeObserver for automatic re-centering on resize
    if (this.uiDesignCanvas?.nativeElement && typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const _entry of entries) {
          // Debounce resize events
          clearTimeout(this.resizeTimeout);
          this.resizeTimeout = setTimeout(() => {
            this.updateCanvasContainerSize();
            this.centerCanvasOnNodes();
            this.logger.info('🔄 Canvas auto-centered after resize');
          }, 150);
        }
      });

      resizeObserver.observe(this.uiDesignCanvas.nativeElement);

      // Store observer for cleanup
      this.canvasResizeObserver = resizeObserver;
    }

    this.logger.info('🎯 Auto-centering setup complete');
  }

  /**
   * Get canvas transform style
   */
  getCanvasTransformStyle(): string {
    return this.uiDesignCanvasService.getTransformStyle();
  }

  /**
   * Get canvas zoom percentage
   */
  getCanvasZoomPercentage(): number {
    return this.uiDesignCanvasService.getZoomPercentage();
  }

  /**
   * Check if canvas is at minimum zoom
   */
  isCanvasAtMinZoom(): boolean {
    return this.uiDesignCanvasService.isAtMinZoom();
  }

  /**
   * Check if canvas is at maximum zoom
   */
  isCanvasAtMaxZoom(): boolean {
    return this.uiDesignCanvasService.isAtMaxZoom();
  }

  /**
   * Track by function for UI Design nodes
   */
  trackByUIDesignNode(_index: number, node: UIDesignNode): string {
    return node.id;
  }

  /**
   * Get context-specific loading text for UI Design nodes
   */
  getLoadingText(title: string): string {
    // If title contains "Editing" and "...", it's a regeneration loading message
    if (title.includes('Editing') && title.includes('...')) {
      return title; // Return the context-specific message as-is
    }

    // Default loading text for initial generation
    return 'Generating wireframe...';
  }

  /**
   * Subscribe to new polling response processor observables
   * ENHANCED: Complete isolation for UI Design workflow - NO POLLING
   */
  private subscribeToNewPollingProcessor(): void {
    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    // Subscribe to current progress changes
    this.subscription.add(
      this.newPollingResponseProcessor.currentProgress$.subscribe(progress => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling progress in UI Design mode - maintaining isolation');
          return;
        }

        this.currentProgress = progress;
        this.handleNewProgressChange(progress);
        this.updatePromptBarEnabledState();
      })
    );

    // Subscribe to current status changes
    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling status in UI Design mode - maintaining isolation');
          return;
        }

        this.currentStatus = status;
        this.handleNewStatusChange(status);
        this.updatePromptBarEnabledState();
      })
    );

    // Subscribe to progress description changes
    this.subscription.add(
      this.newPollingResponseProcessor.progressDescription$.subscribe(description => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling description in UI Design mode - maintaining isolation');
          return;
        }

        this.newProgressDescription = description;
        this.updateStepperDescription(description);
      })
    );

    // Subscribe to log content changes
    this.subscription.add(
      this.newPollingResponseProcessor.logContent$.subscribe(content => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling logs in UI Design mode - maintaining isolation');
          return;
        }

        this.newLogContent = content;
        this.updateLogWindow(content);
      })
    );

    // Subscribe to artifact data changes
    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(data => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling artifacts in UI Design mode - maintaining isolation');
          return;
        }

        this.newArtifactData = data;
        this.updateArtifactsWindow(data);
      })
    );

    // Subscribe to file list changes
    this.subscription.add(
      this.newPollingResponseProcessor.fileList$.subscribe(files => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling file list in UI Design mode - maintaining isolation');
          return;
        }

        this.newFileList = files;
      })
    );

    // Subscribe to code files changes
    this.subscription.add(
      this.newPollingResponseProcessor.codeFiles$.subscribe(files => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling code files in UI Design mode - maintaining isolation');
          return;
        }

        this.newCodeFiles = files;
        this.updateCodeViewer(files);
      })
    );

    // Subscribe to preview URL changes from DEPLOY + COMPLETED state
    // **REQUIREMENT**: URL extraction from metadata with type "ref_code"
    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(url => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling preview URL in UI Design mode - maintaining isolation');
          return;
        }

        // Component received URL from polling response processor
        this.logger.info('📡 Received preview URL from service:', url);
        this.logger.info('📋 Current progress:', this.newPollingResponseProcessor.getCurrentProgress());
        this.logger.info('📋 Current status:', this.newPollingResponseProcessor.getCurrentStatus());

        this.updatePreviewWindow(url);

        // **CRITICAL**: Only process if this is a valid ref_code URL from DEPLOY state
        if (url && url.trim() !== '' && url !== 'ERROR_DEPLOYMENT_FAILED') {
          this.logger.info('🎯 Valid ref_code URL received, marking as priority URL');

          // **CRITICAL**: Mark immediately to prevent Azure URL override
          this.hasNewPollingResponseUrl = true;

          // **CRITICAL FIX**: Force enable preview for URL extraction
          this.isNewPreviewEnabled = true;

          // Update component state
          this.newPreviewUrl = url;

          this.logger.info('🔍 Preview enabled status after URL update:', this.isNewPreviewEnabled);
          this.logger.info('🚫 Azure URL generation will be blocked');

          // **REQUIREMENT**: Process URL and sanitize for iframe display (already called above)
          this.updatePreviewWindow(url);
        } else if (url === 'ERROR_DEPLOYMENT_FAILED') {
          this.logger.info('❌ Deployment failed URL received');
          this.showDeploymentErrorInPreview();
        } else {
          // this.logger.info('🔍 Empty or invalid URL received, not processing');
        }
      })
    );

    // Subscribe to project info changes
    this.subscription.add(
      this.newPollingResponseProcessor.projectInfo$.subscribe(projectInfo => {
        // 🛡️ CRITICAL: Block ALL polling responses in UI Design mode
        if (this.isUIDesignMode$.value) {
          this.logger.info('🚫 Blocking polling project info in UI Design mode - maintaining isolation');
          return;
        }

        this.currentProjectInfo = projectInfo;
        this.updateProjectInfo(projectInfo);
      })
    );

    // Subscribe to code sharing service for file updates (for edit functionality)
    this.subscription.add(
      this.codeSharingService.generatedCode$.subscribe(generatedCode => {
        if (generatedCode && this.isCodeGenerationComplete) {
          this.handleCodeUpdate(generatedCode);
        }
      })
    );
  }

  /**
   * Handle new progress state changes from the new polling response processor
   */
  private handleNewProgressChange(progress: ProgressState | null): void {
    if (!progress) return;

    // this.logger.info('New progress state detected:', progress);

    // ENHANCED: Check if we should show stored layout artifact
    this.checkStoredLayoutForDisplay(progress);

    switch (progress) {
      case 'OVERVIEW':
        this.handleProjectOverviewState();
        break;
      case 'SEED_PROJECT_INITIALIZED':
        this.handleSeedProjectInitializedState();
        break;
      case 'FILE_QUEUE':
        this.handleFileQueueState();
        break;
      case 'Design_System Analyzed':
        this.handleDesignSystemState();
        break;
      case 'COMPONENTS_CREATED':
        this.handleComponentsCreatedState();
        break;
      case 'Layout Analyzed':
      case 'LAYOUT_ANALYZED':
        this.handleLayoutAnalyzedState();
        break;
      case 'PAGES_GENERATED':
        this.handlePagesGeneratedState();
        break;
      case 'BUILD':
        this.handleBuildState();
        break;
      case 'DEPLOY':
      case 'deployed':
        this.handleDeployedState();
        break;
    }

    // Update stepper with new progress
    this.previousProgressState = this.currentProgressState;
    this.currentProgressState = progress;
    this.cdr.detectChanges();
  }

  /**
   * Check if stored layout should be displayed when progress changes
   * ENHANCED: Only shows layout artifact when progress moves FROM LAYOUT_ANALYZED to next state
   */
  private checkStoredLayoutForDisplay(currentProgress: ProgressState): void {
    // Check if we have stored layout and progress has moved from LAYOUT_ANALYZED
    if (this.detectedLayoutFromPrevMetadata &&
        this.previousProgressState === 'LAYOUT_ANALYZED' &&
        currentProgress !== 'LAYOUT_ANALYZED' &&
        currentProgress !== 'Layout Analyzed' &&
        !this.shouldShowLayoutArtifact) {

      this.logger.info('🎯 Progress moved from LAYOUT_ANALYZED to', currentProgress, '- showing stored layout artifact');

      // Create the layout artifact
      const layoutKey = this.detectedLayoutFromPrevMetadata;
      const layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
      const layoutName = this.layoutMapping[layoutKey];

      const artifactItem = {
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl
      };

      // Add to artifacts
      this.artifactsData.push(artifactItem);

      // Update layoutAnalyzedData for artifacts display
      this.layoutAnalyzedData = [{
        key: layoutKey,
        name: layoutName,
        imageUrl: layoutImageUrl
      }];

      // Mark as loaded for persistence
      this.loadedArtifacts.add('Layout Analyzed');
      this.hasLayoutAnalyzed = true;
      this.shouldShowLayoutArtifact = true;

      // Enable artifacts tab now that we have artifacts
      this.enableArtifactsTabIfNeeded();

      this.logger.info('✅ Layout Analyzed artifact added to artifacts window');
    }
  }

  /**
   * Handle new status changes from the new polling response processor
   */
  private handleNewStatusChange(status: StatusType | null): void {
    if (!status) return;

    // this.logger.info('New status detected:', status);

    switch (status) {
      case 'IN_PROGRESS':
        this.showProgressIndicator();
        break;
      case 'COMPLETED':
        this.showCompletionState();
        break;
      case 'FAILED':
        this.showErrorState();
        break;
    }

    // Update polling status for backward compatibility
    this.pollingStatus = status;
    this.cdr.detectChanges();
  }

  /**
   * Update stepper description with new progress description
   */
  private updateStepperDescription(description: string): void {
    this.lastProgressDescription = description;
    this.cdr.detectChanges();
  }

  /**
   * Update prompt bar enabled state based on current progress and status
   * REQUIREMENT: Prompt bar should only be enabled when progress is DEPLOY and status is COMPLETED or FAILED
   */
  private updatePromptBarEnabledState(): void {
    const currentProgress = this.currentProgress;
    const currentStatus = this.currentStatus;

    // Enable prompt bar only for DEPLOY + (COMPLETED | FAILED)
    const shouldEnable = currentProgress === 'DEPLOY' &&
                        (currentStatus === 'COMPLETED' || currentStatus === 'FAILED');

    // Update the state if it has changed
    if (this.isPromptBarEnabled !== shouldEnable) {
      this.isPromptBarEnabled = shouldEnable;

      // this.logger.info('🎯 Prompt bar enabled state updated:', {
      //   currentProgress,
      //   currentStatus,
      //   isPromptBarEnabled: this.isPromptBarEnabled,
      //   shouldEnable
      // });

      // Trigger change detection to update the UI
      this.cdr.detectChanges();
    }
  }

  /**
   * Update log window with new content
   */
  private updateLogWindow(content: string): void {
    if (content) {
      // Process the enhanced log content
      const timestamp = new Date().toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      const logMessage = `${timestamp} - INFO - ${content}`;
      this.logMessages = [...this.logMessages, logMessage];
      this.hasLogs = true;
      this.isLogsTabEnabled = true;

      // Auto-enable logs tab when logs become available for the first time
      this.autoEnableLogsTabIfNeeded();

      this.cdr.detectChanges();
    }
  }

  /**
   * Auto-enable logs tab when logs become available for the first time
   * Only switches if user hasn't manually selected a tab
   */
  private autoEnableLogsTabIfNeeded(): void {
    // Only auto-enable if:
    // 1. Logs tab hasn't been auto-enabled before
    // 2. User hasn't manually selected a tab
    // 3. Logs are now available
    if (!this.logsTabAutoEnabled && !this.userSelectedTab && this.hasLogs) {
      // this.logger.info('Auto-enabling logs tab - logs are now available');

      // Mark as auto-enabled to prevent repeated auto-switching
      this.logsTabAutoEnabled = true;

      // Switch to logs view without setting userSelectedTab flag
      this.autoSwitchToLogsView();
    }
  }

  /**
   * Auto-switch to logs view without marking as user-selected
   * This allows automatic switching while preserving user choice tracking
   */
  private autoSwitchToLogsView(): void {
    // Only proceed if logs are available
    if (!this.hasLogs) {
      return;
    }

    // this.logger.info('Auto-switching to logs view');

    // Update tab states - keep the current view but set logs active flag using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isLogsActive$.next(true);
    this.isArtifactsActive$.next(false);

    // Set the current view to logs
    this.currentView$.next('logs');

    // Don't show loading spinner, we'll show loading animation in the view if no logs
    this.isLoading$.next(false);

    // FIXED: Don't re-process logs when auto-switching to logs
    // The logs are already processed and stored in formattedLogMessages
    // Just trigger change detection and scroll to bottom if we have formatted logs
    if (this.formattedLogMessages.length > 0) {
      // Trigger change detection to ensure the view updates
      this.cdr.detectChanges();

      // Scroll to bottom to show latest logs
      this.scrollLogsToBottom();
    }

    // View will update automatically via reactive patterns
  }

  /**
   * Update Project Overview artifact - REPLACE existing, don't add new
   */
  private updateProjectOverviewArtifact(content: string): void {
    // this.logger.info('🔄 Updating Project Overview artifact with new content');

    // Find existing Project Overview artifact
    const projectOverviewIndex = this.artifactsData.findIndex(item => item.name === 'Project Overview');

    if (projectOverviewIndex !== -1) {
      // Update existing Project Overview
      this.artifactsData[projectOverviewIndex].content = content;
      // this.logger.info('✅ Updated existing Project Overview artifact');
    } else {
      // Only create new if none exists (should not happen with our initialization)
      // this.artifactsData.push({
      //   name: 'Project Overview',
      //   type: 'markdown',
      //   content: content
      // });
      // this.logger.info('🆕 Created new Project Overview artifact (fallback)');
    }

    // Enable artifacts tab
    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  /**
   * Update Design System artifact - REPLACE existing, don't add new
   */
  private updateDesignSystemArtifact(artifactData: any): void {
    // this.logger.info('🔄 Updating Design System artifact with new data');

    // Find existing Design System artifact
    const designSystemIndex = this.artifactsData.findIndex(item => item.name === 'Design System');

    if (designSystemIndex !== -1) {
      // Update existing Design System
      this.artifactsData[designSystemIndex].content = artifactData.tokens || artifactData.content;
      // this.logger.info('✅ Updated existing Design System artifact');
    } else {
      // Only create new if none exists
      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: artifactData.tokens || artifactData.content
      });
      // this.logger.info('🆕 Created new Design System artifact');
    }

    // Enable artifacts tab
    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  /**
   * Update artifacts window with new artifact data
   * Enhanced to use the new artifact processing system
   */
  private updateArtifactsWindow(artifactData: any): void {
    if (!artifactData) {
      return;
    }

    this.logger.info('Updating artifacts window with new data:', artifactData);

    // Use the enhanced artifact processing method
    this.processArtifactData(artifactData);

    // Legacy fallback for backward compatibility
    switch (artifactData.type) {
      case 'readme':
        this.displayReadmeContent(artifactData.content);
        break;
      case 'layout':
        this.displayLayoutContent(artifactData.layoutCode);
        break;
      case 'design-tokens':
        this.displayDesignTokens(artifactData.tokens);
        break;
    }

    // Enable artifacts tab
    this.isArtifactsTabEnabled = true;
    this.cdr.detectChanges();
  }

  /**
   * Update code viewer with new code files
   */
  private updateCodeViewer(files: FileData[]): void {
    if (files && files.length > 0) {
      // Convert FileData to FileModel format
      const fileModels: FileModel[] = files.map(file => ({
        name: file.path,
        type: 'file',
        content: file.code,
        fileName: file.path
      }));

      this.files$.next(fileModels);
      this.isCodeTabEnabled = true;
      this.isCodeGenerationComplete = true;
      this.cdr.detectChanges();
    }
  }

  /**
   * Handle code updates from the code sharing service (for edit functionality)
   */
  private handleCodeUpdate(generatedCode: any): void {
    try {
      this.logger.info('🔄 Handling code update from edit response');

      // Update the files observable with new code
      if (Array.isArray(generatedCode)) {
        // Convert to FileModel format
        const fileModels: FileModel[] = generatedCode.map(file => ({
          name: file.fileName || file.name || 'unknown.txt',
          type: 'file',
          content: file.content || '',
          fileName: file.fileName || file.name || 'unknown.txt'
        }));

        this.files$.next(fileModels);
        this.logger.info(`✅ Updated ${fileModels.length} files in code viewer`);
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {
        // Convert object format to array
        const fileModels: FileModel[] = Object.entries(generatedCode).map(([path, content]) => ({
          name: path,
          type: 'file',
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
          fileName: path
        }));

        this.files$.next(fileModels);
        this.logger.info(`✅ Updated ${fileModels.length} files in code viewer from object format`);
      }

      // Trigger change detection
      this.cdr.detectChanges();

      // If code viewer component is available, refresh it
      if (this.codeViewer) {
        setTimeout(() => {
          this.codeViewer.refreshOpenFiles();
        }, 100);
      }

    } catch (error) {
      this.logger.error('❌ Error handling code update:', error);
    }
  }

  /**
   * Update preview window with new URL
   *
   * **STRICT REQUIREMENTS IMPLEMENTATION**:
   * - Only processes URLs when progress === "DEPLOY" AND status === "COMPLETED"
   * - Uses Angular's DomSanitizer.bypassSecurityTrustResourceUrl() for URL sanitization
   * - Validates URL exists before sanitization
   * - Triggers change detection after updating urlSafe property
   * - Integrates with "once shown, always shown" tab visibility logic
   * - **PRIORITY**: New polling response URLs override legacy deployment URLs
   */
  private updatePreviewWindow(url: string): void {
    this.logger.debug('Update preview window called', {
      url: url,
      isNewPreviewEnabled: this.isNewPreviewEnabled
    });

    if (url && url.trim() !== '' && this.isNewPreviewEnabled) {
      this.logger.info('✅ Processing valid URL for preview iframe with enhanced validation');

      // **CRITICAL**: Mark that we have a new polling response URL to prevent legacy override
      this.hasNewPollingResponseUrl = true;
      this.logger.info('🎯 Marking new polling response URL as active - will prevent legacy override');

      // Log URL extraction details
      this.logger.info('🎯 Extracted URL from polling response:', {
        url: url.trim(),
        type: 'ref_code',
        progress: 'DEPLOY',
        status: 'COMPLETED',
        timestamp: new Date().toISOString()
      });

      // Use enhanced URL processing with availability checking
      this.processUrlForIframe(url.trim());

    } else if (url === 'ERROR_DEPLOYMENT_FAILED') {
      this.logger.info('❌ Deployment failed - showing error in preview');
      this.showDeploymentErrorInPreview();

    } else {
      this.logger.info('🚫 Preview disabled - invalid URL or preview not enabled');
      this.logger.info('📋 URL provided:', url);
      this.logger.info('📋 Preview enabled:', this.isNewPreviewEnabled);

      // Reset iframe state
      this.isIframeReady$.next(false);
      this.isUrlValidated$.next(false);
      this.isUrlAvailable$.next(false);

      this.isPreviewTabEnabled = false;
      this.previewIcon$.next('bi-code-slash');
      this.urlSafe = undefined; // Clear sanitized URL
    }

    // **REQUIREMENT**: Trigger change detection after updating urlSafe property
    this.cdr.detectChanges();
    this.logger.info('🔄 Change detection triggered after preview window update');
  }

  /**
   * Validate URL format for preview iframe
   * Ensures the URL is safe and valid before sanitization
   *
   * **SECURITY REQUIREMENTS**:
   * - Only allows HTTP and HTTPS protocols
   * - Validates URL structure and format
   * - Prevents malicious URL injection
   */
  private isValidPreviewUrl(url: string): boolean {
    try {
      // **REQUIREMENT**: Basic string validation
      if (!url || typeof url !== 'string' || url.trim() === '') {
        this.logger.warn('🔒 Preview URL validation failed: empty or invalid string');
        this.urlValidationError$.next('URL is empty or invalid');
        return false;
      }

      // **REQUIREMENT**: Create URL object for validation
      const urlObject = new URL(url.trim());

      // **REQUIREMENT**: Only allow HTTP and HTTPS protocols for security
      const isValidProtocol = urlObject.protocol === 'http:' || urlObject.protocol === 'https:';

      if (!isValidProtocol) {
        this.logger.warn('🔒 Preview URL validation failed: invalid protocol', urlObject.protocol);
        this.logger.warn('📋 Only HTTP and HTTPS protocols are allowed for iframe security');
        this.urlValidationError$.next(`Invalid protocol: ${urlObject.protocol}. Only HTTP and HTTPS are allowed.`);
        return false;
      }

      // **REQUIREMENT**: Validate hostname exists
      if (!urlObject.hostname || urlObject.hostname.trim() === '') {
        this.logger.warn('🔒 Preview URL validation failed: missing or empty hostname');
        this.urlValidationError$.next('URL hostname is missing or empty');
        return false;
      }

      this.logger.info('🔒 Preview URL validation passed:', {
        protocol: urlObject.protocol,
        hostname: urlObject.hostname,
        pathname: urlObject.pathname
      });

      // Clear any previous validation errors
      this.urlValidationError$.next('');
      return true;
    } catch (error) {
      this.logger.warn('🔒 Preview URL validation failed: invalid URL format', error instanceof Error ? error.message : 'Unknown error');
      this.urlValidationError$.next(error instanceof Error ? error.message : 'Invalid URL format');
      return false;
    }
  }

  /**
   * Enhanced URL processing with availability checking and iframe loading management
   * Only loads iframe when URL is confirmed available and validated
   */
  private processUrlForIframe(url: string): void {
    this.logger.debug('Processing URL for iframe', {
      url: url,
      urlType: typeof url,
      urlLength: url?.length
    });

    // Reset iframe state
    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);

    // Step 1: Validate URL format
    if (!this.isValidPreviewUrl(url)) {
      this.logger.error('🔒 URL validation failed, cannot load iframe');
      return;
    }
    this.isUrlValidated$.next(true);

    // Step 2: Check URL availability (basic check)
    this.checkUrlAvailability(url.trim())
      .then(isAvailable => {
        this.logger.debug('URL availability check result', { isAvailable });

        if (isAvailable) {
          this.isUrlAvailable$.next(true);

          // Step 3: Sanitize and prepare URL for iframe
          this.prepareUrlForIframe(url.trim());
        } else {
          this.logger.warn('🌐 URL is not available, cannot load iframe');
          this.urlValidationError$.next('URL is not accessible');
        }
      })
      .catch(error => {
        this.logger.error('🌐 Error checking URL availability:', error);
        this.urlValidationError$.next('Error checking URL availability');
      });
  }

  /**
   * Check if URL is available and accessible
   * Uses a simple fetch request to verify the URL responds
   */
  private async checkUrlAvailability(url: string): Promise<boolean> {
    try {
      this.logger.debug('Checking URL availability', { url });

      // Use a simple HEAD request to check if URL is accessible
      // Note: This might be blocked by CORS, but we'll try anyway
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      try {
        const response = await fetch(url, {
          method: 'HEAD',
          signal: controller.signal,
          mode: 'no-cors' // Allow cross-origin requests
        });

        clearTimeout(timeoutId);

        this.logger.debug('Fetch response received', {
          status: response.status,
          type: response.type
        });

        // For no-cors mode, we can't check status, so we assume success if no error
        return true;
      } catch (fetchError) {
        clearTimeout(timeoutId);

        // If CORS blocks the request, we'll assume the URL is available
        // since the iframe will handle CORS differently
        if (fetchError instanceof Error && fetchError.name === 'TypeError') {
          this.logger.info('🌐 CORS blocked HEAD request, assuming URL is available for iframe');
          return true;
        }

        throw fetchError;
      }
    } catch (error) {
      this.logger.debug('URL availability check failed', { error });

      // For most deployment URLs, CORS will block our check
      // So we'll be optimistic and assume the URL is available
      this.logger.info('🌐 URL availability check failed (likely CORS), assuming URL is available');
      return true;
    }
  }

  /**
   * Prepare and sanitize URL for iframe loading
   * Final step before iframe is shown
   */
  private prepareUrlForIframe(url: string): void {
    this.logger.debug('Preparing URL for iframe', { url });

    try {
      // Sanitize URL for iframe
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(url);

      this.logger.debug('URL sanitized for iframe', {
        sanitizedUrlType: typeof this.urlSafe
      });

      // Update deployed URL observable
      this.deployedUrl$.next(url);

      // Mark iframe as ready to load
      this.isIframeReady$.next(true);

      // Enable preview tab and clear errors
      this.isPreviewTabEnabled = true;
      this.isPreviewLoading$.next(false);
      this.previewIcon$.next('bi-eye');
      this.previewError$.next(false);

      this.logger.info('🎯 Iframe is ready to load with validated and available URL');

      // Trigger change detection
      this.cdr.detectChanges();

    } catch (error) {
      this.logger.error('🔧 Error preparing URL for iframe:', error);
      this.urlValidationError$.next('Error preparing URL for iframe');
    }
  }

  /**
   * Update project information and display in artifacts
   * Updated to handle new polling response format
   */
  private updateProjectInfo(projectInfo: ProjectInfo | null): void {
    if (!projectInfo) {
      // this.logger.warn('No project info provided to updateProjectInfo');
      return;
    }

    // this.logger.info('Updating project info from new polling processor:', projectInfo);

    // Update project name for display in header
    if (projectInfo.name && projectInfo.name.trim() !== '') {
      this.projectName = projectInfo.name;
      this.isProjectNameLoading = false;
      // this.logger.info('Project name updated to:', this.projectName);
    }

    // Update project name in the project overview artifact if it exists
    const projectOverviewIndex = this.artifactsData.findIndex(item => item.name === 'Project Overview');
    if (projectOverviewIndex !== -1) {
      // Update the project overview content to include the project name
      const currentContent = this.artifactsData[projectOverviewIndex].content;
      const updatedContent = currentContent.replace(
        /# .*/,
        `# ${projectInfo.name}`
      );
      this.artifactsData[projectOverviewIndex].content = updatedContent;
      // this.logger.info('Updated project overview artifact with project name');
    }

    // Trigger change detection to update the UI
    this.cdr.detectChanges();
  }

  // State-specific handlers for new polling response format

  /**
   * Handle project overview state
   */
  private handleProjectOverviewState(): void {
    // this.logger.info('Handling project overview state');
    // Enable artifacts tab for project overview using enhanced logic
    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  /**
   * Handle SEED_PROJECT_INITIALIZED state
   */
  private handleSeedProjectInitializedState(): void {
    // this.logger.info('Handling SEED_PROJECT_INITIALIZED state');
    // No artifacts for this state, only logs and stepper description
  }

  /**
   * Handle FILE_QUEUE state
   */
  private handleFileQueueState(): void {
    // this.logger.info('Handling FILE_QUEUE state');
    // No artifacts for this state, only logs and stepper description
  }

  /**
   * Handle COMPONENTS_CREATED state
   */
  private handleComponentsCreatedState(): void {
    // this.logger.info('Handling COMPONENTS_CREATED state');
    // No artifacts for this state, only logs and stepper description
  }

  /**
   * Handle PAGES_GENERATED state
   */
  private handlePagesGeneratedState(): void {
    // this.logger.info('Handling PAGES_GENERATED state');
    // No artifacts for this state, only logs and stepper description
  }

  /**
   * Handle layout analyzed state
   */
  private handleLayoutAnalyzedState(): void {
    // this.logger.info('Handling layout analyzed state');
    this.hasLayoutAnalyzed = true;
    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  /**
   * Handle design system state
   */
  private handleDesignSystemState(): void {
    // this.logger.info('Handling design system state');
    this.hasDesignSystem = true;
    this.isArtifactsTabEnabled = this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  /**
   * Handle BUILD state
   * Shows complete code files when progress is BUILD and status is IN_PROGRESS or COMPLETED
   */
  private handleBuildState(): void {
    // this.logger.info('🔧 Handling BUILD state');

    // Subscribe to current status to handle BUILD IN_PROGRESS specifically
    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
        if (currentProgress === 'BUILD' && (status === 'IN_PROGRESS' || status === 'COMPLETED')) {
          // this.logger.info(`🎯 BUILD state with ${status} status detected`);

          // Update tab states based on enhanced logic
          this.isCodeTabEnabled = this.newPollingResponseProcessor.isCodeTabEnabled();
          // this.logger.info('📊 Code tab enabled status:', this.isCodeTabEnabled);
        }
      })
    );

    // Subscribe to code files from the processor
    this.subscription.add(
      this.newPollingResponseProcessor.codeFiles$.subscribe(files => {
        if (files && files.length > 0) {
          // this.logger.info('🔧 BUILD state: Received code files for code viewer:', files.length, 'files');

          // Convert FileData to FileModel format for the code viewer with proper tree structure
          const fileModels = files.map(file => {
            // Use the full path as the name to maintain tree structure
            const fullPath = file.path;
            const fileName = this.extractFileName(file.path);
            const language = this.getLanguageFromPath(file.path);

            // this.logger.info(`📄 Processing file: ${fullPath} (${language}) - ${file.code.length} chars`);

            return {
              name: fullPath, // Use full path for tree structure
              language: language,
              content: file.code,
              path: fullPath,
              type: 'file' as const
            };
          });

          // Update files for code viewer
          this.files$.next(fileModels);
          // this.logger.info('✅ Files updated in code viewer:', fileModels.length, 'files');

          // Enable code tab and switch to it immediately for BUILD state
          this.isCodeTabEnabled = true;
          // this.logger.info('🎯 Code tab enabled for BUILD state, switching to code view');
          this.toggleCodeView();
        }
      })
    );
  }

  /**
   * Handle deployed state (DEPLOY or deployed)
   * STRICT: Only use URL from ref_code metadata, no fallbacks
   */
  private handleDeployedState(): void {
    this.logger.info('🚀 Handling DEPLOY state - STRICT mode (ref_code only)');

    // Subscribe to preview URL from the processor
    if (!this.subscription) {
      this.subscription = new Subscription();
    }

    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(url => {
        this.logger.info('🔄 Preview URL subscription triggered with URL:', url);

        if (url && url.trim() !== '') {
          this.logger.info('🎉 DEPLOY state: Received NEW preview URL:', url);

          // Check if this is an error URL
          if (url === 'ERROR_DEPLOYMENT_FAILED') {
            this.logger.info('❌ Deployment failed - showing error page in preview');
            this.showDeploymentErrorInPreview();
          } else {
            this.logger.info('✅ Processing ref_code URL (STRICT):', url);

            // Clear previous URL data first
            this.logger.info('🧹 Clearing previous URL data');
            this.newPreviewUrl = '';
            this.urlSafe = undefined;

            // Set new URL data
            this.newPreviewUrl = url.trim();
            this.deployedUrl$.next(url.trim());

            // Sanitize the ref_code URL for the iframe
            this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(url.trim());
            this.logger.info('🔒 ref_code URL sanitized for iframe (STRICT):', url.trim());

            // Enable ONLY the preview tab for DEPLOY state
            this.isPreviewTabEnabled = true;
            this.previewError$.next(false); // Clear any previous errors
            this.isLoading$.next(false); // Stop loading
            this.isPreviewLoading$.next(false); // Stop preview loading

            // Immediately switch to preview tab to show the NEW deployed app
            this.togglePreviewView();

            this.logger.info('🎯 Preview tab enabled with ref_code URL (STRICT)');
            this.logger.info('🆕 Current preview URL from ref_code:', this.newPreviewUrl);
          }
        } else if (url === '') {
          this.logger.info('🧹 Empty URL received - clearing preview (STRICT)');
          // Clear preview when empty URL is received
          this.newPreviewUrl = '';
          this.urlSafe = undefined;
          this.deployedUrl$.next('');
        }
      })
    );

    // Subscribe to current status to handle DEPLOY COMPLETED specifically
    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        const currentProgress = this.newPollingResponseProcessor.getCurrentProgress();
        if (currentProgress === 'DEPLOY') {
          if (status === 'COMPLETED') {
            // this.logger.info('🎉 DEPLOY state with COMPLETED status detected - processing new deployment');

            // ENHANCED: Check if regeneration was in progress and complete it
            if (this.isRegenerationInProgress$.value) {
              this.logger.info('🎯 Regeneration deployment completed - same URL updated with new content');
              this.completeRegenerationAfterDeployment();
            }

            // The preview URL subscription above will handle the URL processing
          } else if (status === 'IN_PROGRESS') {
            // this.logger.info('🔄 DEPLOY state with IN_PROGRESS status - clearing previous deployment');
            // Clear previous deployment when new deployment starts
            this.clearPreviousDeployment();
          }
        }
      })
    );
  }

  /**
   * Clear previous deployment data to ensure clean new deployment
   */
  private clearPreviousDeployment(): void {
    // this.logger.info('🧹 Clearing previous deployment data in component');
    this.newPreviewUrl = '';
    this.urlSafe = undefined;
    this.deployedUrl$.next('');
    this.previewError$.next(false);
    // this.logger.info('✅ Previous deployment data cleared in component');
  }

  /**
   * Show deployment error in preview tab with retry mechanism
   */
  private showDeploymentErrorInPreview(): void {
    // this.logger.info('Showing deployment error in preview tab');
    this.previewError$.next(true);
    this.isPreviewTabEnabled = true;

    // Switch to preview tab to show error if not already selected by user
    if (!this.userSelectedTab) {
      this.togglePreviewView();
    }
  }

  // Enhanced tab visibility logic using new polling response processor
  get isNewArtifactsTabEnabled(): boolean {
    return this.newPollingResponseProcessor.isArtifactsTabEnabled();
  }

  get isNewCodeTabEnabled(): boolean {
    // Once shown, always shown logic
    if (this.codeTabEverShown) {
      // this.logger.info('🔍 Code tab visibility check: Already shown before, keeping visible');
      return true;
    }

    // STRICT: Only enable code tab for BUILD + IN_PROGRESS
    const isEnabled = this.newPollingResponseProcessor.isCodeTabEnabled();

    // If the tab should be shown now, mark it as ever shown
    if (isEnabled) {
      this.codeTabEverShown = true;
      // this.logger.info('🎯 Code tab enabled for the first time - will remain visible');
    }

    // this.logger.info('🔍 Code tab visibility check:', {
    //   isEnabled,
    //   codeTabEverShown: this.codeTabEverShown,
    //   currentProgress: this.newPollingResponseProcessor.getCurrentProgress(),
    //   currentStatus: this.newPollingResponseProcessor.getCurrentStatus()
    // });
    return isEnabled;
  }

  get isNewLogsTabEnabled(): boolean {
    return true; // Always enabled for new polling response
  }

  get isNewPreviewTabEnabled(): boolean {
    // Once shown, always shown logic
    if (this.previewTabEverShown) {
      // this.logger.info('🔍 Preview tab visibility check: Already shown before, keeping visible');
      return true;
    }

    // ENHANCED: Show preview tab for FAILED status or DEPLOY + COMPLETED
    const isEnabled = this.newPollingResponseProcessor.isPreviewEnabled() || this.isFailedState;

    // If the tab should be shown now, mark it as ever shown
    if (isEnabled) {
      this.previewTabEverShown = true;
      // this.logger.info('🎯 Preview tab enabled for the first time - will remain visible');
    }

    // this.logger.info('🔍 Preview tab visibility check:', {
    //   isEnabled,
    //   previewTabEverShown: this.previewTabEverShown,
    //   currentProgress: this.newPollingResponseProcessor.getCurrentProgress(),
    //   currentStatus: this.newPollingResponseProcessor.getCurrentStatus()
    // });
    return isEnabled;
  }

  // ENHANCED: Get preview tab name observable for dynamic naming
  get previewTabName(): Observable<string> {
    return this.previewTabName$.asObservable();
  }

  // ENHANCED: Get error state for template access
  get isInFailedState(): boolean {
    return this.isFailedState;
  }

  // ENHANCED: Get regeneration state for template access
  get regenerationInProgress$(): Observable<boolean> {
    return this.isRegenerationInProgress$.asObservable();
  }

  // Status-specific handlers

  /**
   * Show progress indicator for IN_PROGRESS status
   */
  private showProgressIndicator(): void {
    this.isLoading$.next(true);
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);
  }

  /**
   * Show completion state for COMPLETED status
   */
  private showCompletionState(): void {
    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.previewError$.next(false);
    this.isCodeGenerationComplete = true;
  }

  /**
   * Show error state for FAILED status
   * ENHANCED: Shows error page in preview tab and changes tab name to "Error"
   */
  private showErrorState(): void {
    this.logger.info('🚨 FAILED status detected - showing error page in preview tab');

    // Set error state flags
    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.previewError$.next(true);
    this.isFailedState = true;

    // Change preview tab name to "Error"
    this.previewTabName$.next('Error');

    // Set error description
    this.errorDescription$.next('An error occurred during processing. Please try again.');

    // Extract detailed error message from progress description if available
    this.extractErrorMessageFromProgressDescription();

    // Enable preview tab to show error page
    this.isPreviewTabEnabled = true;
    this.previewTabEverShown = true;

    // Switch to preview tab to show error if not already selected by user
    if (!this.userSelectedTab) {
      this.logger.info('🎯 Auto-switching to preview tab to show error page');
      this.togglePreviewView();
      // Reset the flag since this was an automatic switch, not user-initiated
      this.userSelectedTab = false;
    } else {
      // Force the current view to be preview
      this.currentView$.next('preview');
    }

    // Force change detection
    this.cdr.detectChanges();

    this.logger.info('✅ Error state configured - preview tab renamed to "Error"');
  }

  // Content display helpers for artifacts window

  /**
   * Display README content in artifacts window
   */
  private displayReadmeContent(content: string): void {
    const readmeIndex = this.artifactsData.findIndex(item => item.name === 'README');
    if (readmeIndex !== -1) {
      this.artifactsData[readmeIndex].content = content;
    } else {
      // this.artifactsData.push({
      //   name: 'Project Overview',
      //   type: 'markdown',
      //   content: content
      // });
    }
  }

  /**
   * Display layout content in artifacts window
   * ENHANCED: Creates proper layout artifact with correct image URL
   */
  private displayLayoutContent(layoutCode: string): void {
    this.logger.info('🎯 displayLayoutContent called with layoutCode:', layoutCode);

    // Prevent duplicates first
    this.preventDuplicateLayoutAnalyzed();

    // Validate layout code
    if (!layoutCode || !this.layoutMapping[layoutCode]) {
      this.logger.warn('Invalid layout code provided to displayLayoutContent:', layoutCode);
      return;
    }

    // Create proper image URL
    const imageUrl = `assets/images/layout-${layoutCode}.png`;
    const layoutName = this.layoutMapping[layoutCode];

    this.logger.info('🖼️ Creating layout artifact with:', { layoutCode, imageUrl, layoutName });

    const layoutIndex = this.artifactsData.findIndex(item => item.name === 'Layout Analyzed');
    if (layoutIndex !== -1) {
      // Update existing artifact with proper image URL
      this.artifactsData[layoutIndex].content = imageUrl;
      this.logger.info('✅ Updated existing Layout Analyzed artifact with image URL');
    } else {
      // Create new artifact with proper image URL
      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: imageUrl
      });
      this.logger.info('✅ Created new Layout Analyzed artifact with image URL');
    }

    // Update layoutData for preview display
    this.layoutData = [layoutCode];

    // Update layoutAnalyzedData for artifacts display
    this.layoutAnalyzedData = [{
      key: layoutCode,
      name: layoutName,
      imageUrl: imageUrl
    }];

    this.hasLayoutAnalyzed = true;
    this.logger.info('🎯 Layout content display complete:', {
      layoutData: this.layoutData,
      layoutAnalyzedData: this.layoutAnalyzedData
    });
  }

  /**
   * Display design tokens in artifacts window
   */
  private displayDesignTokens(tokens: DesignTokensData): void {
    const designSystemIndex = this.artifactsData.findIndex(item => item.name === 'Design System');
    if (designSystemIndex !== -1) {
      this.artifactsData[designSystemIndex].content = tokens;
    } else {
      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: tokens
      });
    }
    this.hasDesignSystem = true;
    this.designSystemData = tokens;
  }


  /**
   * After view initialization, connect the chat-window's showImagePreview method to our code-window's showImagePreview method
   */
  ngAfterViewInit(): void {
    // Use setTimeout to ensure the chat-window component is fully initialized
    setTimeout(() => {
      // Override the chat-window's showImagePreview method to use our full-screen overlay
      if (this.chatWindow) {
        // Override the method
        this.chatWindow.showImagePreview = (imageUrl: string, imageName: string = 'Image') => {
          // Call our showImagePreview method instead
          this.showImagePreview(imageUrl, imageName);
        };
      }
    }, 0);

    this.setupPanelWidthObserver();
    this.setupResizerEventListener();
  }

  /**
   * Set up the resizer event listener
   */
  private setupResizerEventListener(): void {
    // Wait for the DOM to be ready
    setTimeout(() => {
      const resizer = document.querySelector('.resizer') as HTMLElement;
      if (resizer) {
        resizer.addEventListener('mousedown', (event: MouseEvent) => {
          this.startResize(event);
        });
        // this.logger.info('Resizer event listener set up successfully');
      } else {
        // this.logger.warn('Resizer element not found, retrying in 100ms');
        // Retry after a short delay
        setTimeout(() => this.setupResizerEventListener(), 100);
      }
    }, 100);
  }

    /**
   * Set up observer to monitor left panel width and hide project name when too narrow
   */
  private setupPanelWidthObserver(): void {
    // Use ResizeObserver to monitor the left panel width
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const width = entry.contentRect.width;
          // Hide project name when panel width is less than 400px
          this.shouldHideProjectName$.next(width < 400);
          this.cdr.detectChanges();
        }
      });

      // Observe the left panel element
      setTimeout(() => {
        const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
        if (leftPanel) {
          resizeObserver.observe(leftPanel);
        }
      }, 100);
    } else {
      // Fallback for browsers that don't support ResizeObserver
      // Use window resize event as a fallback
      const checkPanelWidth = () => {
        const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
        if (leftPanel) {
          const width = leftPanel.offsetWidth;
          this.shouldHideProjectName$.next(width < 400);
          this.cdr.detectChanges();
        }
      };

      // Check on window resize
      window.addEventListener('resize', checkPanelWidth);

      // Initial check
      setTimeout(checkPanelWidth, 100);
    }
  }

  /**
   * Subscribe to app state and initialize component
   */
  private initializeAppState(): void {
    // Subscribe to the app state to get project and job IDs
    this.subscription = this.appStateService.project$.subscribe(projectState => {
      // Update project ID and job ID if they've changed
      if (projectState.projectId && projectState.projectId !== this.projectId) {
        // If we have a new project ID, update the stepper state service
        if (this.projectId) {
          // This means we're switching from one project to another
          // Trigger a stepper reset
          this.stepperStateService.triggerStepperReset();
        }

        this.projectId = projectState.projectId;

        // Update the stepper state service with the new project ID
        this.stepperStateService.setCurrentProjectId(this.projectId);
      }

      if (projectState.jobId && projectState.jobId !== this.jobId) {
        this.jobId = projectState.jobId;

        // Azure URL generation removed - using Netlify URLs from deployment response
      }

      // Initialize chat with user prompt if available and we haven't done it yet
      // CRITICAL: Skip this initialization in UI Design mode to prevent duplicate user messages
      if (projectState.prompt && this.lightMessages.length === 0 && !this.isUIDesignMode$.value) {
        // Add the user prompt to the chat messages (only for non-UI Design mode)
        this.lightMessages = [
          { text: projectState.prompt, from: 'user', theme: 'light' },
          {
            text: "I'm generating code based on your request. Please wait while I process your input...",
            from: 'ai',
            theme: 'light',
          },
        ];
        this.logger.info('📝 Initialized chat messages for code generation mode');
      } else if (this.isUIDesignMode$.value) {
        this.logger.info('🚫 Skipped chat initialization - UI Design mode handles its own chat messages');
      }

      // If we have both IDs and polling hasn't started, start it
      // 🛡️ CRITICAL: Block polling service in UI Design mode
      if (this.projectId && this.jobId && !this.isPolling && !this.isUIDesignMode$.value) {
        this.pollingService.startPolling(this.projectId, this.jobId, {
          taskType: 'code generation',
          // Using fixed 1-second polling interval
        });
        this.updatePollingStatus(true);

        // Azure URL generation removed - using Netlify URLs from deployment response
      } else if (this.isUIDesignMode$.value) {
        this.logger.info('🚫 Blocking polling service start in UI Design mode - maintaining isolation');
      }
    });

    // No need for interval check anymore as we're using the AppStateService

    // Show preview tab initially with loading animation using BehaviorSubjects
    this.currentView$.next('preview');
    this.isLoading$.next(true);
    this.isPreviewLoading$.next(true);
    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);
    this.isLogsActive$.next(false);

    // Only enable preview and logs tabs initially
    this.isLogsTabEnabled = true;
    this.isPreviewTabEnabled = true;
    this.isCodeTabEnabled = false; // Code tab will be enabled when code is generated

    // Azure URL generation removed - using Netlify URLs from deployment response

    // OLD POLLING RESPONSE FUNCTIONS - COMMENTED OUT
    // Subscribe to polling service status updates
    // this.subscription = this.pollingService.status$.subscribe(status => {
    //   // Update the polling status for the stepper
    //   const newStatus = status === 'completed' ? 'COMPLETED' :
    //     status === 'failed' || status === 'error' ? 'FAILED' : 'IN_PROGRESS';

    //   // Only update if the status has changed to avoid unnecessary updates
    //   if (this.pollingStatus !== newStatus) {
    //     this.logger.info(`Updating polling status from ${this.pollingStatus} to ${newStatus}`);
    //     this.pollingStatus = newStatus;

    //     // If the status is 'completed', make sure we have the correct artifacts
    //     if (status === 'completed') {
    //       this.logger.info('Status is completed, checking artifacts');

    //       // We don't want to remove artifacts when the status is completed
    //       // Instead, we'll just log the current state
    //       this.logger.info('Layout analyzed flag:', this.hasLayoutAnalyzed);
    //       this.logger.info('Design system flag:', this.hasDesignSystem);

    //       // Check if we have the artifacts in the list
    //       const hasLayoutAnalyzedFile = this.artifactsData.some(file => file.name === 'Layout Analyzed');
    //       const hasDesignSystemFile = this.artifactsData.some(file => file.name === 'Design System');

    //       this.logger.info('Layout analyzed in artifacts:', hasLayoutAnalyzedFile);
    //       this.logger.info('Design system in artifacts:', hasDesignSystemFile);
    //     }

    //     // **HARD CODED PREVIEW - Show when stepper completes**
    //     if (newStatus === 'COMPLETED') {
    //       this.logger.info('✅ Stepper COMPLETED - showing hard coded preview');
    //       this.showHardCodedPreview();
    //     }

    //     // Handle FAILED state - show error page in preview
    //     if (newStatus === 'FAILED') {
    //       this.logger.info('Detected FAILED state, showing error page in preview');
    //       this.isLoading$.next(false);
    //       this.isPreviewLoading$.next(false);
    //       this.previewError$.next(true);

    //       // We need to set isCodeGenerationComplete to true to show the error page,
    //       // but we don't want to show the code tab when there's an error
    //       this.isCodeGenerationComplete = true; // Mark as complete to show error page

    //       // Ensure code tab is not active when there's an error using BehaviorSubjects
    //       this.isCodeActive$.next(false);
    //       this.isPreviewActive$.next(true);
    //       this.isLogsActive$.next(false);

    //       // Get the error message from the polling service
    //       // This will extract the error message from the latest status response
    //       this.extractErrorMessageFromProgressDescription();

    //       // Switch to preview view to show the error page
    //       if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
    //         this.logger.info('Auto-switching to preview tab to show error page');
    //         this.togglePreviewView();
    //         // Reset the flag since this was an automatic switch, not user-initiated
    //         this.userSelectedTab = false;
    //       } else {
    //         // Force the current view to be preview
    //         this.currentView$.next('preview');
    //       }
    //     }
    //   }

    // OLD POLLING RESPONSE FUNCTIONS - COMMENTED OUT
    // if (status === 'completed') {
    //   this.isLoading$.next(false);
    //   this.isPreviewLoading$.next(false); // Ensure preview loading is also disabled

    //   // **HARD CODED PREVIEW - Show when polling completes**
    //   this.logger.info('✅ Polling COMPLETED - showing hard coded preview');
    //   this.showHardCodedPreview();

    //   // Only mark code generation as complete and enable code tab if there's no error
    //   if (!this.previewError$.value) {
    //     this.isCodeGenerationComplete = true; // Set the flag to indicate code generation is complete
    //     this.isCodeTabEnabled = true; // Enable the code tab
    //     this.logger.info('Code generation completed successfully, enabling code tab');

    //     // Clear any existing toasts before showing a new one
    //     this.toastService.clear();

    //     // Show success toast notification only if we're not navigating away
    //     if (this.router.url.includes('/experience/generate')) {
    //       this.toastService.success('Code generation completed successfully. Preview is ready.');
    //     }
    //   } else {
    //     this.logger.info('Code generation completed with errors, code tab remains disabled');
    //   }

    //   // Empty the layoutData array when state is finished
    //   this.layoutData = [];

    //   // Switch to preview view only if user hasn't manually selected a tab
    //   if (!this.userSelectedTab) {
    //     this.togglePreviewView();
    //   } else {
    //     // Still need to update the tab states without changing the view using BehaviorSubjects
    //     this.isCodeActive$.next(false);
    //     this.isPreviewActive$.next(true);
    //     this.isLogsActive$.next(false);
    //   }

    //   // Force check if iframe exists after a short delay
    //   setTimeout(() => {
    //     const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
    //     if (!iframe) {
    //       this.logger.warn(
    //         'Iframe not found in DOM after status completed, forcing reload of preview view'
    //       );
    //       // Only force a refresh of the view if we're already in preview view
    //       if (this.currentView$.value === 'preview') {
    //         this.currentView$.next('editor');
    //         setTimeout(() => {
    //           this.currentView$.next('preview');
    //         }, 50);
    //       } else {
    //         this.logger.info('Not forcing refresh since we are not in preview view');
    //       }
    //     }
    //   }, 200);

    //   // Add a message to the chat when code generation is completed
    // } else if (status === 'failed' || status === 'error') {
    //   this.isLoading$.next(false);
    //   this.isPreviewLoading$.next(false); // Ensure preview loading is also disabled
    //   this.previewError$.next(true);
    //   this.isCodeGenerationComplete = true; // Mark as complete to show error page
    //   this.isCodeTabEnabled = false; // Disable the code tab when there's an error

    //   // Clear any existing toasts before showing a new one
    //   this.toastService.clear();

    //   // Show error toast notification only if we're not navigating away
    //   if (this.router.url.includes('/experience/generate')) {
    //     this.toastService.error('Code generation failed.');
    //   }

    //   // Ensure code tab is not active when there's an error using BehaviorSubjects
    //   this.isCodeActive$.next(false);
    //   this.isPreviewActive$.next(true);
    //   this.isLogsActive$.next(false);

    //   // Extract error message from progress description
    //   // This will extract the error message from the latest status response
    //   this.extractErrorMessageFromProgressDescription();

    //   // Switch to preview view to show the error page
    //   if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
    //     this.logger.info('Auto-switching to preview tab to show error page');
    //     this.togglePreviewView();
    //     // Reset the flag since this was an automatic switch, not user-initiated
    //     this.userSelectedTab = false;
    //   } else {
    //     // Force the current view to be preview
    //     this.currentView$.next('preview');
    //   }
    // }
    // });

    // OLD POLLING RESPONSE FUNCTIONS - COMMENTED OUT
    // Subscribe to polling service progress updates
    // this.subscription.add(
    //   this.pollingService.progress$.subscribe(progress => {
    //     if (progress) {
    //       // Add a progress message to the chat if it's not empty
    //       this.logger.info('Polling progress updated:', progress);

    //       // Update the loading message with the progress
    //       const lastMessage = this.lightMessages[this.lightMessages.length - 1];
    //       if (
    //         lastMessage &&
    //         lastMessage.from === 'ai' &&
    //         lastMessage.text.includes('generating code')
    //       ) {
    //         // Update the last message instead of adding a new one
    //         lastMessage.text = progress;
    //       }

    //       // Check for specific progress states and update artifacts accordingly
    //       if (progress === StepperState.LAYOUT_ANALYZED) {
    //         this.logger.info('LAYOUT_ANALYZED progress state detected');
    //         // Set the flag that layout analyzed data is available
    //         this.hasLayoutAnalyzed = true;
    //         // Capture the layout analyzed state permanently in artifacts
    //         this.captureLayoutAnalyzedState();
    //       } else if (progress === StepperState.DESIGN_SYSTEM_MAPPED) {
    //         this.logger.info('DESIGN_SYSTEM_MAPPED progress state detected');
    //         // Set the flag that design system data is available
    //         this.hasDesignSystem = true;
    //         // Capture the design system mapped state permanently in artifacts
    //         this.captureDesignSystemMappedState();
    //       }

    //       // Update the loading messages with the progress
    //       // this.loadingMessages = [...this.loadingMessages.slice(0, 4), progress];

    //       // Track the current progress state
    //       this.currentProgressState = progress;
    //       // Only disable loading when we reach specific states AND have valid layout data
    //       if (
    //         (progress === StepperState.LAYOUT_ANALYZED ||
    //           progress === StepperState.PAGES_GENERATED ||
    //           progress === StepperState.COMPONENTS_CREATED ||
    //           progress === StepperState.BUILD_SUCCEEDED) &&
    //         this.layoutData &&
    //         this.layoutData.length > 0 &&
    //         this.layoutMapping[this.layoutData[0]]
    //       ) {
    //         this.isPreviewLoading$.next(false);
    //         this.isLoading$.next(false);
    //       } else if (
    //         !this.layoutData ||
    //         this.layoutData.length === 0 ||
    //         !this.layoutMapping[this.layoutData[0]]
    //       ) {
    //         // If we don't have valid layout data, ensure loading is shown
    //         this.isPreviewLoading$.next(true);
    //         this.isLoading$.next(true);
    //         this.logger.info('No valid layout data, showing loading screen');
    //       }

    //       // Handle specific progress states
    //       if (
    //         progress === StepperState.LAYOUT_ANALYZED ||
    //         progress === StepperState.DESIGN_SYSTEM_MAPPED
    //       ) {
    //         this.logger.info('Layout analyzed or Design System mapped state detected:', progress);

    //         // Clear any existing toasts before showing a new one
    //         this.toastService.clear();

    //         // Clear any existing timer
    //         if (this.autoSwitchToLogsTimer) {
    //           clearTimeout(this.autoSwitchToLogsTimer);
    //         }

    //         // Handle specific states
    //         if (progress === StepperState.LAYOUT_ANALYZED) {
    //           // Set the flag that layout analyzed data is available
    //           this.hasLayoutAnalyzed = true;
    //         } else if (progress === StepperState.DESIGN_SYSTEM_MAPPED) {
    //           // Set the flag that design system data is available
    //           this.hasDesignSystem = true;
    //         }

    //         // If we don't have layout data, keep it empty and show loading
    //         if (
    //           !this.layoutData ||
    //           this.layoutData.length === 0 ||
    //           !this.layoutMapping[this.layoutData[0]]
    //         ) {
    //           // Don't use default fallback
    //           this.isLayoutLoading = true;
    //           this.isPreviewLoading$.next(true);
    //           this.isLoading$.next(true);
    //         } else {
    //           // If we have valid layout data, hide loading
    //           this.isLayoutLoading = false;
    //           this.isPreviewLoading$.next(false);
    //           this.isLoading$.next(false);
    //         }

    //         // Process multiple layouts if present
    //         if (this.layoutData && this.layoutData.length > 0) {
    //           this.logger.info(`${this.layoutData.length} layout(s) detected, using all valid layouts`);
    //           // Keep all valid layouts instead of just the first one
    //           this.layoutData = this.layoutData.filter(key => this.layoutMapping[key]);

    //           // Log the image paths for each layout key
    //           if (this.layoutData.length > 0) {
    //             this.layoutData.forEach(_key => {
    //               // Placeholder for future implementation if needed
    //             });
    //           }
    //         }
    //       } else if (progress === StepperState.PAGES_GENERATED) {
    //         this.logger.info('Pages generated state detected');

    //         // Clear any existing toasts before showing a new one
    //         this.toastService.clear();


    //         // Clear any existing timer
    //         if (this.autoSwitchToLogsTimer) {
    //           clearTimeout(this.autoSwitchToLogsTimer);
    //         }



    //         // If we don't have layout data, keep it empty and show loading
    //         if (
    //           !this.layoutData ||
    //           this.layoutData.length === 0 ||
    //           !this.layoutMapping[this.layoutData[0]]
    //         ) {
    //           this.logger.info(
    //             'No layout data detected in PAGES_GENERATED state, keeping empty array and showing loading'
    //           );
    //           // Don't use default fallback
    //           this.isLayoutLoading = true;
    //           this.isPreviewLoading$.next(true);
    //           this.isLoading$.next(true);
    //         } else {
    //           // If we have valid layout data, hide loading after a short delay
    //           this.isLayoutLoading = true;

    //           // Set a timeout to simulate loading for 1 second
    //           setTimeout(() => {
    //             this.isLayoutLoading = false;
    //             this.isPreviewLoading$.next(false);
    //             this.isLoading$.next(false);
    //           }, 1000);
    //         }

    //         // Set timer to switch to logs tab after 20 seconds, but only if user hasn't manually selected a tab
    //         this.autoSwitchToLogsTimer = setTimeout(() => {
    //           if (!this.userSelectedTab) {
    //             this.logger.info(
    //               'Auto-switching to logs tab after 20 seconds (user has not manually selected a tab)'
    //             );
    //             this.toggleLogsView();
    //             // Reset the flag since this was an automatic switch, not user-initiated
    //             this.userSelectedTab = false;
    //           } else {
    //             this.logger.info('Respecting user tab selection, not auto-switching to logs');
    //           }
    //         }, 20000); // 20 seconds
    //       } else if (progress === StepperState.BUILD_STARTED) {
    //         this.logger.info('Build started state detected');

    //         // Clear any existing toasts before showing a new one
    //         this.toastService.clear();


    //         // Clear any existing timer
    //         if (this.autoSwitchToLogsTimer) {
    //           clearTimeout(this.autoSwitchToLogsTimer);
    //         }

    //         // Switch to logs view to show build progress
    //         if (this.currentView$.value !== 'logs' && !this.userSelectedTab) {
    //           this.logger.info(
    //             'Auto-switching to logs tab for build progress (user has not manually selected a tab)'
    //           );
    //           this.toggleLogsView();
    //           // Reset the flag since this was an automatic switch, not user-initiated
    //           this.userSelectedTab = false;
    //         }
    //       } else if (progress === StepperState.BUILD_SUCCEEDED) {
    //         this.logger.info('Build succeeded state detected');

    //         // Clear any existing toasts before showing a new one
    //         this.toastService.clear();

    //         // Show toast notification for build success only if we're not navigating away
    //         if (this.router.url.includes('/experience/generate')) {
    //           this.toastService.success('Application successfully built.');
    //         }

    //         // Switch to preview view to show the final result
    //         if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
    //           this.logger.info(
    //             'Auto-switching to preview tab for successful build (user has not manually selected a tab)'
    //           );
    //           this.togglePreviewView();
    //           // Reset the flag since this was an automatic switch, not user-initiated
    //           this.userSelectedTab = false;
    //         }

    //         // Only enable the code tab if there's no error
    //         if (!this.previewError$.value) {
    //           this.isCodeTabEnabled = true;
    //           this.isCodeGenerationComplete = true;
    //           this.logger.info('Build succeeded, enabling code tab');
    //         } else {
    //           this.logger.info('Build succeeded but errors exist, code tab remains disabled');
    //         }
    //       } else if (progress === StepperState.BUILD_FAILED) {
    //         this.logger.info('Build failed state detected');

    //         // Clear any existing toasts before showing a new one
    //         this.toastService.clear();

    //         // Show toast notification for build failure only if we're not navigating away
    //         if (this.router.url.includes('/experience/generate')) {
    //           this.toastService.error('Build got failed.');
    //         }

    //         // Set error state using BehaviorSubjects
    //         this.isLoading$.next(false);
    //         this.isPreviewLoading$.next(false);
    //         this.previewError$.next(true);
    //         this.isCodeGenerationComplete = true; // Mark as complete to show error page

    //         // Ensure code tab is not active when there's an error using BehaviorSubjects
    //         this.isCodeActive$.next(false);
    //         this.isPreviewActive$.next(true);
    //         this.isLogsActive$.next(false);

    //         // Extract error message from progress description
    //         // This will extract the error message from the latest status response
    //         this.extractErrorMessageFromProgressDescription();

    //         // Switch to preview view to show the error page
    //         if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
    //           this.logger.info('Auto-switching to preview tab to show error page');
    //           this.togglePreviewView();
    //           // Reset the flag since this was an automatic switch, not user-initiated
    //           this.userSelectedTab = false;
    //         } else {
    //           // Force the current view to be preview
    //           this.currentView$.next('preview');
    //         }
    //       } else if (progress === StepperState.OVERVIEW) {
    //         this.logger.info('Project Overview state detected');

    //         // Enable the Artifacts tab when Project Overview is complete
    //         this.isArtifactsTabEnabled = true;

    //         // Add Project Overview to artifacts data if not already present
    //         const hasOverview = this.artifactsData.some(file => file.name === 'Project Overview');
    //         if (!hasOverview) {
    //           // Get the project overview content from the last progress description
    //           let overviewContent = this.lastProgressDescription;
    //           if (!overviewContent) {
    //             overviewContent = '# Project Overview\n\nProject overview information will be displayed here.';
    //           }

    //           // this.artifactsData.push({
    //           //   name: 'Project Overview',
    //           //   type: 'markdown',
    //           //   content: overviewContent
    //           // });

    //           // Select the Project Overview file by default
    //           if (this.artifactsData.length === 1) {
    //             this.selectArtifactFile(this.artifactsData[0]);
    //           }
    //         }

    //         // Auto-switch to artifacts tab if user hasn't manually selected a tab
    //         if (!this.userSelectedTab) {
    //           this.logger.info('Auto-switching to artifacts tab after Project Overview completion');
    //           this.toggleArtifactsView();
    //           // Reset the flag since this was an automatic switch, not user-initiated
    //           this.userSelectedTab = false;
    //         }

    //         // If user hasn't selected a tab, switch to artifacts view
    //         if (!this.userSelectedTab) {
    //           this.toggleArtifactsView();
    //           // Reset the flag since this was an automatic switch
    //           this.userSelectedTab = false;
    //         }
    //       } else if (progress === StepperState.LAYOUT_ANALYZED) {
    //         this.logger.info('Layout Analyzed state detected');

    //         // Capture the layout analyzed state permanently in artifacts
    //         this.captureLayoutAnalyzedState();
    //       } else if (progress === StepperState.DESIGN_SYSTEM_MAPPED) {
    //         this.logger.info('Design System Mapped state detected');

    //         // Capture the design system mapped state permanently in artifacts
    //         this.captureDesignSystemMappedState();
    //       } else if (progress === StepperState.DEPLOYED) {
    //         this.logger.info('Process deployed state detected');

    //         // Clear any existing toasts before showing a new one
    //         this.toastService.clear();

    //         // Show toast notification for process completion only if we're not navigating away
    //         if (this.router.url.includes('/experience/generate')) {
    //           this.toastService.success('Code generation completed successfully.');
    //         }

    //         // Switch to preview view for the final result
    //         if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
    //           this.logger.info(
    //             'Auto-switching to preview tab for completed process (user has not manually selected a tab)'
    //           );
    //           this.togglePreviewView();
    //           // Reset the flag since this was an automatic switch, not user-initiated
    //           this.userSelectedTab = false;
    //         }

    //         // Enable tabs based on the state
    //         if (!this.previewError$.value) {
    //           // Enable all tabs when the process is complete without errors
    //           this.isCodeTabEnabled = true;
    //           this.isCodeGenerationComplete = true;
    //           this.logger.info('Process completed successfully, enabling all tabs');
    //         } else {
    //           // Only enable preview and logs tabs when there are errors
    //           this.isCodeTabEnabled = false;
    //           this.logger.info('Process completed with errors, code tab remains disabled');
    //         }

    //         this.isPreviewTabEnabled = true;
    //         this.isLogsTabEnabled = true;
    //       }
    //     }
    //   })
    // );

    // OLD POLLING RESPONSE FUNCTIONS - COMMENTED OUT
    // Subscribe to polling service progress description updates
    // this.subscription.add(
    //   this.pollingService.progressDescription$.subscribe(description => {
    //     if (description && description.trim() !== '') {
    //       this.logger.info('Received progress description from polling service:', description);

    //       // Add the progress description to the chat window with a typewriter effect
    //       this.lastProgressDescription = description;
    //       // this.addProgressDescriptionToChat(description);

    //       // Check for specific progress states in the description and update artifacts accordingly
    //       if (description.includes('LAYOUT_ANALYZED')) {
    //         this.logger.info('LAYOUT_ANALYZED detected in progress description');
    //         // Set the flag that layout analyzed data is available
    //         this.hasLayoutAnalyzed = true;
    //         // Capture the layout analyzed state permanently in artifacts
    //         this.captureLayoutAnalyzedState();
    //       } else if (description.includes('DESIGN_SYSTEM_MAPPED')) {
    //         this.logger.info('DESIGN_SYSTEM_MAPPED detected in progress description');
    //         // Set the flag that design system data is available
    //         this.hasDesignSystem = true;
    //         // Capture the design system mapped state permanently in artifacts
    //         this.captureDesignSystemMappedState();
    //       }

    //       // Try to parse layout data from the description
    //       if (description.includes('{') && description.includes('}')) {
    //         try {
    //           const jsonStartIndex = description.indexOf('{');
    //           const jsonEndIndex = description.lastIndexOf('}') + 1;

    //           if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
    //             const jsonPart = description.substring(jsonStartIndex, jsonEndIndex);
    //             const parsedData = JSON.parse(jsonPart);

    //             // Check if it contains layout data
    //             if (parsedData && typeof parsedData === 'object') {
    //               // Look for data property that might contain layout keys
    //               const layoutKeys = parsedData.data;
    //               this.logger.debug(
    //                 'Layout keys from description data field:',
    //                 layoutKeys,
    //                 'Type:',
    //                 typeof layoutKeys
    //               );

    //               if (layoutKeys) {
    //                 // Reset layout data
    //                 this.layoutData = [];

    //                 // Process layout keys based on their type
    //                 if (typeof layoutKeys === 'string') {
    //                   // If it's a single layout key as a string
    //                   if (this.layoutMapping[layoutKeys]) {
    //                     this.layoutData = [layoutKeys];
    //                     this.logger.debug(
    //                       'Using single valid layout key:',
    //                       layoutKeys,
    //                       'Display name:',
    //                       this.layoutMapping[layoutKeys]
    //                     );
    //                   } else {
    //                     // If the key is not valid, clear the layout data to trigger loading state
    //                     this.layoutData = [];
    //                     this.logger.debug('Invalid layout key in string data:', layoutKeys);
    //                   }
    //                 } else if (Array.isArray(layoutKeys)) {
    //                   // Filter only valid layout keys that exist in our mapping
    //                   const validKeys = layoutKeys.filter(
    //                     key => typeof key === 'string' && this.layoutMapping[key]
    //                   );
    //                   // Use all valid keys
    //                   if (validKeys.length > 0) {
    //                     this.layoutData = validKeys;
    //                     this.logger.debug(`Using ${validKeys.length} valid layout keys:`, validKeys);
    //                     validKeys.forEach(key => {
    //                       this.logger.debug(`- ${key}: ${this.layoutMapping[key]}`);
    //                     });
    //                   } else {
    //                     // If no valid keys found, clear the layout data to trigger loading state
    //                     this.layoutData = [];
    //                     this.logger.debug('No valid layout keys found in array data');
    //                   }
    //                 } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {
    //                   // Extract keys from the object
    //                   const validKeys = Object.keys(layoutKeys).filter(
    //                     key => this.layoutMapping[key]
    //                   );
    //                   // Use all valid keys
    //                   if (validKeys.length > 0) {
    //                     this.layoutData = validKeys;
    //                     this.logger.debug(
    //                       `Using ${validKeys.length} valid layout keys from object:`,
    //                       validKeys
    //                     );
    //                     validKeys.forEach(key => {
    //                       this.logger.debug(`- ${key}: ${this.layoutMapping[key]}`);
    //                     });
    //                   } else {
    //                     // If no valid keys found, clear the layout data to trigger loading state
    //                     this.layoutData = [];
    //                     this.logger.debug('No valid layout keys found in object data');
    //                   }
    //                 }

    //                 this.logger.debug('Extracted layout key from description:', this.layoutData);

    //                 // If no layouts were detected, keep the array empty (don't use default fallback)
    //                 if (this.layoutData.length === 0) {
    //                   this.logger.debug(
    //                     'No valid layout keys detected in progress description, keeping empty array'
    //                   );
    //                 } else {
    //                   // If we have valid layout data, capture the state for artifacts based on the description
    //                   if (description.includes('LAYOUT_ANALYZED')) {
    //                     // Capture the layout analyzed state
    //                     this.captureLayoutAnalyzedState();
    //                     this.logger.info('Captured layout analyzed state from progress description');
    //                   } else if (description.includes('DESIGN_SYSTEM_MAPPED')) {
    //                     // Store the design system data for later use
    //                     this.designSystemData = parsedData.data;
    //                     // Capture the design system mapped state
    //                     this.captureDesignSystemMappedState();
    //                     this.logger.info('Captured design system mapped state from progress description');
    //                   }
    //                 }

    //                 // No need for loading delay - show immediately
    //                 this.isLayoutLoading = false;
    //               }
    //             }
    //           }
    //         } catch (e) {
    //           this.logger.error('Error parsing layout data from description:', e);
    //         }
    //       }

    //       // Enable the logs tab since we have progress updates
    //       this.isLogsTabEnabled = true;
    //     }
    //   })
    // );

    // Subscribe to NEW polling response processor logs updates (no state filtering)
    this.subscription.add(
      this.newPollingResponseProcessor.logContent$.subscribe(logContent => {
        if (logContent && logContent.trim() !== '') {
          // this.logger.info('Received accumulated log content from new polling processor:', logContent.length, 'characters');

          // Split accumulated log content into individual log lines for processing
          const logLines = logContent.split('\n').filter(line => line.trim() !== '');
          // this.logger.info('Processing', logLines.length, 'log lines');

          // Process all log lines immediately without typewriter effect
          this.startLogStreaming(logLines);

          // Set hasLogs to true to enable the logs tab
          this.hasLogs = true;
          // this.logger.info('Logs available from new processor, enabling logs tab');

          // Auto-enable logs tab when logs become available for the first time
          this.autoEnableLogsTabIfNeeded();

          // We no longer need to set isLogsTabEnabled since we're using hasLogs to control tab clickability

          // Look for logs that might contain JSON data with layout information
          for (const log of logLines) {
            if (typeof log === 'string' && log.includes('"message"') && log.includes('"data"')) {
              try {
                // Extract the JSON part from the log
                const jsonStartIndex = log.indexOf('{');
                if (jsonStartIndex !== -1) {
                  const jsonPart = log.substring(jsonStartIndex);
                  const parsedData = JSON.parse(jsonPart);

                  // Check if it has the expected structure
                  if (parsedData.message && parsedData.data) {
                    // this.logger.info('Found layout data in logs:', parsedData);

                    // Directly extract the layout keys from the data field
                    const layoutKeys = parsedData.data;
                    // this.logger.info(
                    //   'Layout keys from data field:',
                    //   layoutKeys,
                    //   'Type:',
                    //   typeof layoutKeys
                    // );

                    // Reset layout data
                    this.layoutData = [];

                    // Process layout keys based on their type
                    if (typeof layoutKeys === 'string') {
                      // If it's a single layout key as a string
                      if (this.layoutMapping[layoutKeys]) {
                        this.layoutData = [layoutKeys];
                      }
                    } else if (Array.isArray(layoutKeys)) {
                      // Filter only valid layout keys that exist in our mapping
                      const validKeys = layoutKeys.filter(
                        key => typeof key === 'string' && this.layoutMapping[key]
                      );
                      // Only use the first valid key
                      if (validKeys.length > 0) {
                        this.layoutData = [validKeys[0]];
                      }
                    } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {
                      // Extract keys from the object
                      const validKeys = Object.keys(layoutKeys).filter(
                        key => this.layoutMapping[key]
                      );
                      // Only use the first valid key
                      if (validKeys.length > 0) {
                        this.layoutData = [validKeys[0]];
                      }
                    }

                    // this.logger.info('Extracted layout key from logs:', this.layoutData);

                    // If no layouts were detected, keep the array empty (don't use default fallback)
                    if (this.layoutData.length === 0) {
                      // this.logger.info('No valid layout keys detected in logs, keeping empty array');
                    } else {
                      // If we have valid layout data, capture the state for artifacts based on the log content
                      if (log.includes('LAYOUT_ANALYZED')) {
                        // Capture the layout analyzed state
                        this.captureLayoutAnalyzedState();
                        // this.logger.info('Captured layout analyzed state from logs');
                      } else if (log.includes('DESIGN_SYSTEM_MAPPED')) {
                        // Store the design system data for later use
                        this.designSystemData = parsedData.data;
                        // Capture the design system mapped state
                        this.captureDesignSystemMappedState();
                        // this.logger.info('Captured design system mapped state from logs');
                      }
                    }

                    // No need for loading delay - show immediately
                    this.isLayoutLoading = false;
                  }
                }
              } catch (e) {
                // this.logger.error('Error parsing JSON from log:', e);
              }
            }
          }

          // We don't add logs to the chat window anymore
          // Only handle critical errors in the chat
          this.addLogUpdateToChatWindow(logLines);
        }
      })
    );

    // Subscribe to NEW polling response processor artifact data
    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(artifactData => {
        if (artifactData) {
          // this.logger.info('Received artifact data from new polling processor:', artifactData.type);

          if (artifactData.type === 'text') {
            // Handle project overview/README content - UPDATE existing, don't add new
            this.updateProjectOverviewArtifact(artifactData.content);
          } else if (artifactData.type === 'json') {
            // Handle design system tokens
            this.updateDesignSystemArtifact(artifactData);
          }
        }
      })
    );

    // Subscribe to NEW polling response processor code files
    this.subscription.add(
      this.newPollingResponseProcessor.codeFiles$.subscribe(codeFiles => {
        if (codeFiles && codeFiles.length > 0) {
          // this.logger.info('Received code files from new polling processor:', codeFiles.length, 'files');

          // Convert FileData to the format expected by the code viewer
          const fileModels = codeFiles.map(file => ({
            name: file.path,
            type: 'file' as const,
            content: file.code
          }));

          // Update the code viewer with the new files
          this.generatedCode = fileModels;
          this.isCodeGenerationComplete = true;
          this.processCodeFromService(fileModels);
        }
      })
    );

    // Subscribe to NEW polling response processor preview URL
    this.subscription.add(
      this.newPollingResponseProcessor.previewUrl$.subscribe(previewUrl => {
        if (previewUrl && previewUrl.trim() !== '') {
          // this.logger.info('Received preview URL from new polling processor:', previewUrl);
          this.previewUrl = previewUrl;
          this.isNewPreviewEnabled = true;
        }
      })
    );

    // Subscribe to NEW polling response processor progress and status for stepper
    this.subscription.add(
      this.newPollingResponseProcessor.currentProgress$.subscribe(progress => {
        if (progress) {
          // this.logger.info('Progress update from new polling processor:', progress);
          this.currentProgressState = progress;
          // Progress state updated - stepper will react to this change
        }
      })
    );

    this.subscription.add(
      this.newPollingResponseProcessor.currentStatus$.subscribe(status => {
        if (status) {
          // this.logger.info('Status update from new polling processor:', status);
          // Handle status updates for completion/failure
          if (status === 'COMPLETED') {
            // this.logger.info('✅ Task completed successfully from new processor!');
          } else if (status === 'FAILED') {
            // this.logger.error('❌ Task failed from new processor!');
          }
        }
      })
    );

    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntil(this.destroy$))
      .subscribe((theme: 'light' | 'dark') => {
        this.currentTheme$.next(theme);
        // Update icons based on the new theme
      });

    // Subscribe to UI Design responses from polling
    this.subscription.add(
      this.newPollingResponseProcessor.artifactData$.subscribe(artifactData => {
        if (artifactData && this.isUIDesignArtifact(artifactData)) {
          this.logger.info('🎨 Received UI Design artifact from polling:', artifactData);
          this.handleUIDesignResponse(artifactData.content);
        }
      })
    );

    // Get the generated code from the service
    this.generatedCode = this.codeSharingService.getGeneratedCode();
    // this.logger.info('Generated code from service:', this.generatedCode);

    // If we already have code, process it immediately
    if (this.generatedCode) {
      // this.logger.info('\u2705 Code already available in code-sharing service');
      this.isCodeGenerationComplete = true; // Set the flag to indicate code generation is complete
      this.isPreviewLoading$.next(false); // Ensure preview loading is disabled

      // Empty the layoutData array
      this.layoutData = [];
      // this.logger.info('Code already available, emptied layoutData array:', this.layoutData);

      // Set to preview mode only if user hasn't manually selected a tab
      if (!this.userSelectedTab) {
        this.currentView$.next('preview');
      } else {
        // this.logger.info('Respecting user tab selection, not switching to preview');
      }

      // Force check if iframe exists after a short delay
      setTimeout(() => {
        const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
        // this.logger.debug('Iframe check for existing code:', !!iframe);
        if (!iframe) {
          // this.logger.warn('Iframe not found in DOM for existing code, forcing reload of preview view');
        }
      }, 200);

      this.processCodeFromService(this.generatedCode);
    } else {
      // Check if we need to start polling
      // 🛡️ CRITICAL: Block polling service in UI Design mode
      if (this.projectId && this.jobId && !this.isUIDesignMode$.value) {
        // this.logger.info('\u2705 Starting polling service from code-window component');
        this.pollingService.startPolling(this.projectId, this.jobId, {
          taskType: 'code generation',
          // Using fixed 1-second polling interval
        });
        this.updatePollingStatus(true);

        // Azure URL generation removed - using Netlify URLs from deployment response
      } else if (this.isUIDesignMode$.value) {
        this.logger.info('🚫 Blocking polling service start in UI Design mode - maintaining isolation');
      }
    }

    // Subscribe to future code updates
    this.subscription = this.codeSharingService.generatedCode$.subscribe(code => {
      if (code) {
        // this.logger.info('\u2705 Received new generated code from service');
        this.isCodeGenerationComplete = true; // Set the flag to indicate code generation is complete
        this.processCodeFromService(code);
      }
    });
  }

  /**
   * Process API response for layout data
   * @param response The API response containing progress information
   */
  processApiResponse(response: any): void {
    if (!response || !response.details) return;

    const { progress, progress_description, log } = response.details;

    // Format the progress description before processing
    let formattedDescription = progress_description;
    if (progress_description) {
      // Format LAYOUT_ANALYZED section
      if (progress_description.includes('LAYOUT_ANALYZED')) {
        formattedDescription = this.formatLayoutAnalyzedDescription(progress_description);
      }

      // Add the formatted description to the chat
      if (formattedDescription !== progress_description) {
        // this.addProgressDescriptionToChat(formattedDescription);
      } else {
        // this.addProgressDescriptionToChat(progress_description);
      }
    }

    // Update progress state
    if (progress) {
      this.currentProgressState = progress;

      // If it's a layout-related state, set loading state
      if (progress.includes('LAYOUT_ANALYZED') || progress.includes('DESIGN_SYSTEM_MAPPED')) {
        this.isLayoutLoading = true;

        // Process the log field which contains JSON string with layout data
        if (log && typeof log === 'string') {
          try {
            // Check if log is a JSON string
            if (log.includes('{') && log.includes('}')) {
              // this.logger.info('Found potential JSON in log field:', log);

              // Try to parse the log as JSON
              const parsedLog = JSON.parse(log);

              // Check if it has the expected structure with message and data
              if (parsedLog.message && parsedLog.data) {
                // this.logger.info('Found layout data in log field:', parsedLog);

                // Extract layout keys from the data field
                const layoutKeys = parsedLog.data;
                // this.logger.info('Layout keys from log field:', layoutKeys, 'Type:', typeof layoutKeys);

                // Reset layout data
                this.layoutData = [];

                // Capture the state for artifacts based on the progress
                if (progress.includes('LAYOUT_ANALYZED')) {
                  // We'll capture the layout analyzed state after processing the data
                  // this.logger.info('Will capture layout analyzed state after processing data');
                } else if (progress.includes('DESIGN_SYSTEM_MAPPED')) {
                  // Store the design system data for later use
                  this.designSystemData = parsedLog.data;
                  // this.logger.info('Stored design system data:', this.designSystemData);
                }

                // Process layout keys based on their type
                if (typeof layoutKeys === 'string') {
                  // If it's a single layout key as a string
                  if (this.layoutMapping[layoutKeys]) {
                    this.layoutData = [layoutKeys];
                  }
                } else if (Array.isArray(layoutKeys)) {
                  // Filter only valid layout keys that exist in our mapping
                  const validKeys = layoutKeys.filter(
                    key => typeof key === 'string' && this.layoutMapping[key]
                  );
                  // Only use the first valid key
                  if (validKeys.length > 0) {
                    this.layoutData = [validKeys[0]];
                  }
                } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {
                  // Extract keys from the object
                  const validKeys = Object.keys(layoutKeys).filter(key => this.layoutMapping[key]);
                  // Only use the first valid key
                  if (validKeys.length > 0) {
                    this.layoutData = [validKeys[0]];
                  }
                }
              }
            }
          } catch (e) {
            // this.logger.error('Error parsing JSON from log field:', e);
          }
        }

        // If we don't have layout data, keep it empty and show loading
        if (
          !this.layoutData ||
          this.layoutData.length === 0 ||
          !this.layoutMapping[this.layoutData[0]]
        ) {
          // this.logger.info(
          //   'No layout data detected in API response, keeping empty array and showing loading'
          // );
          // Don't use default fallback
          this.isLayoutLoading = true;
          this.isPreviewLoading$.next(true);
          this.isLoading$.next(true);
        } else {
          // If we have valid layout data, hide loading
          this.isLayoutLoading = false;
          this.isPreviewLoading$.next(false);
          this.isLoading$.next(false);

          // Capture the state for artifacts based on the progress
          if (progress.includes('LAYOUT_ANALYZED')) {
            // Now that we have processed the data, capture the layout analyzed state
            this.captureLayoutAnalyzedState();
            // this.logger.info('Captured layout analyzed state after processing data');
          } else if (progress.includes('DESIGN_SYSTEM_MAPPED')) {
            // Now that we have processed the data, capture the design system mapped state
            this.captureDesignSystemMappedState();
            // this.logger.info('Captured design system mapped state after processing data');
          }
        }
      }
    }

    // Process progress description for layout data as fallback
    if (progress_description && (!this.layoutData || this.layoutData.length === 0)) {
      // Look for JSON data in the description
      if (progress_description.includes('{') && progress_description.includes('}')) {
        try {
          const jsonStartIndex = progress_description.indexOf('{');
          const jsonEndIndex = progress_description.lastIndexOf('}') + 1;

          if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
            const jsonPart = progress_description.substring(jsonStartIndex, jsonEndIndex);
            const parsedData = JSON.parse(jsonPart);

            // Check if it has the expected structure with message and data
            if (parsedData.message && parsedData.data) {
              // this.logger.info('Found layout data in progress description as fallback:', parsedData);

              // Extract layout keys from the data field
              const layoutKeys = parsedData.data;
              // this.logger.info(
              //   'Layout keys from progress description:',
              //   layoutKeys,
              //   'Type:',
              //   typeof layoutKeys
              // );

              // Reset layout data
              this.layoutData = [];

              // Process layout keys based on their type
              if (typeof layoutKeys === 'string') {
                // If it's a single layout key as a string
                if (this.layoutMapping[layoutKeys]) {
                  this.layoutData = [layoutKeys];
                }
              } else if (Array.isArray(layoutKeys)) {
                // If it's an array of layout keys
                this.layoutData = layoutKeys.filter(
                  key => typeof key === 'string' && this.layoutMapping[key]
                );

                // Log the image paths for each layout key
                if (this.layoutData.length > 0) {
                  this.layoutData.forEach(_key => {
                    // Placeholder for future implementation if needed
                  });
                }
              } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {
                // If it's an object, extract the keys
                this.layoutData = Object.keys(layoutKeys).filter(key => this.layoutMapping[key]);

                // Log the image paths for each layout key
                if (this.layoutData.length > 0) {
                  this.layoutData.forEach(_key => {
                    // Placeholder for future implementation if needed
                  });

                  // If we have valid layout data, capture the state for artifacts based on the progress
                  if (progress_description.includes('LAYOUT_ANALYZED')) {
                    // Capture the layout analyzed state
                    this.captureLayoutAnalyzedState();
                    // this.logger.info('Captured layout analyzed state from progress description fallback');
                  } else if (progress_description.includes('DESIGN_SYSTEM_MAPPED')) {
                    // Store the design system data for later use
                    this.designSystemData = parsedData.data;
                    // Capture the design system mapped state
                    this.captureDesignSystemMappedState();
                    // this.logger.info('Captured design system mapped state from progress description fallback');
                  }
                }
              }
            }
          }
        } catch (e) {
          // this.logger.error('Error parsing JSON from progress description:', e);
        }
      }

      // If still no layouts were detected, keep the array empty (don't use default fallback)
      if (!this.layoutData || this.layoutData.length === 0) {
        // this.logger.info('No layout data detected anywhere, keeping empty array');
        // Don't use default fallback
      }
    }
  }

  /**
   * Get a page title based on index and layout key
   * @param index The index of the page
   * @param layoutKey The layout key
   * @returns A page title
   */
  getPageTitle(index: number, layoutKey: string): string {
    // Default page titles for all 8 possible pages
    const defaultTitles = [
      'Home Page',
      'About Us Page',
      'Products Page',
      'Services Page',
      'Blog Page',
      'Contact Page',
      'Gallery Page',
      'FAQ Page',
    ];

    // If we have a layout key, use it to create a more specific title
    if (layoutKey && this.layoutMapping[layoutKey]) {
      // Create a title based on the default title and layout
      return defaultTitles[index % defaultTitles.length] + ` (${layoutKey})`;
    }

    // Return default title if available, otherwise generate a generic one
    return defaultTitles[index % defaultTitles.length] || `Page ${index + 1}`;
  }

  // Process code received from the code-sharing service
  // ENHANCED: Initialize baseline file tree for persistence and comparison
  private processCodeFromService(code: any): void {
    this.logger.info('🔄 Processing code from service and initializing file tree baseline');
    this.generatedCode = code;

    // Convert code to file models
    const fileModels: FileModel[] = [];

    // Handle different formats of code
    if (Array.isArray(this.generatedCode)) {
      // this.logger.info('Processing array format code with', this.generatedCode.length, 'files');

      // Check if it's the new format with fileName and content properties
      if (
        this.generatedCode.length > 0 &&
        'fileName' in this.generatedCode[0] &&
        'content' in this.generatedCode[0]
      ) {
        // This is the format from the polling service with fileName and content properties
        for (const file of this.generatedCode) {
          fileModels.push({
            name: file.fileName,
            type: 'file',
            content:
              typeof file.content === 'string'
                ? file.content
                : JSON.stringify(file.content, null, 2),
          });
        }
        // this.logger.info(
        //   'Processed array of files with fileName/content format, count:',
        //   fileModels.length
        // );
      } else {
        // Handle other array formats
        for (const item of this.generatedCode) {
          if (typeof item === 'object' && item !== null) {
            // Try to extract file information
            const name = item.name || item.fileName || item.path || 'unknown.txt';
            const content = item.content || '';
            fileModels.push({
              name,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            });
          }
        }
        // this.logger.info('Processed generic array format, file count:', fileModels.length);
      }
    } else if (typeof this.generatedCode === 'string') {
      // Try to parse string as JSON
      try {
        const parsedCode = JSON.parse(this.generatedCode);
        // this.logger.info('Parsed string code as JSON:', typeof parsedCode);

        if (Array.isArray(parsedCode)) {
          // Handle array of files
          for (const item of parsedCode) {
            if (typeof item === 'object' && item !== null) {
              const name = item.fileName || item.name || item.path || 'unknown.txt';
              const content = item.content || '';
              fileModels.push({
                name,
                type: 'file',
                content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              });
            }
          }
        } else if (typeof parsedCode === 'object') {
          // Handle object with file paths as keys
          for (const [filePath, content] of Object.entries(parsedCode)) {
            fileModels.push({
              name: filePath,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            });
          }
        }
      } catch (e) {
        // this.logger.warn('Generated code is not valid JSON:', e);
        // Use the string as a single file content
        fileModels.push({
          name: 'output.txt',
          type: 'file',
          content: this.generatedCode,
        });
      }
    } else if (typeof this.generatedCode === 'object' && this.generatedCode !== null) {
      // Handle object format (key-value pairs where key is file path and value is content)
      for (const [filePath, content] of Object.entries(this.generatedCode)) {
        fileModels.push({
          name: filePath,
          type: 'file',
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
        });
      }
      // this.logger.info('Processed object format, file count:', fileModels.length);
    } else {
      // this.logger.warn('Generated code is not in expected format:', typeof this.generatedCode);
    }

    // Get the selected technology from the app state
    const projectState = this.appStateService.getProjectState();
    const technology = projectState.technology || 'angular';
    // this.logger.info('Project state:', projectState);
    // this.logger.info('Ordering files based on selected technology:', technology);

    // Sort the files based on the selected technology
    const sortedFileModels = this.sortFilesByTechnology(fileModels, technology);

    // Log the first few files before and after sorting
    // this.logger.info(
    //   'First 5 files before sorting:',
    //   fileModels.slice(0, 5).map(f => f.name)
    // );
    // this.logger.info(
    //   'First 5 files after sorting:',
    //   sortedFileModels.slice(0, 5).map(f => f.name)
    // );

    // Update the files property if we have any files using BehaviorSubject
    if (sortedFileModels.length > 0) {
      this.files$.next(sortedFileModels);

      // ENHANCED: Initialize baseline file tree for persistence and comparison
      // This creates the initial baseline that will be used for future edit/regenerate comparisons
      this.fileTreePersistenceService.initializeBaseline(sortedFileModels);
      this.logger.info(`🏗️ Initialized file tree baseline with ${sortedFileModels.length} files`);

      // this.logger.info('Updated files property with sorted code, file count:', sortedFileModels.length);
    } else {
      // this.logger.warn('No files were extracted from the generated code');
    }

    // Update flags when code is received using BehaviorSubjects
    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false); // Ensure preview loading is also disabled
    this.isCodeGenerationComplete = true; // Set the flag to indicate code generation is complete

    // Empty the layoutData array when code is received
    this.layoutData = [];
    // this.logger.info('Code received, emptied layoutData array:', this.layoutData);

    // Switch to preview view only if user hasn't manually selected a tab
    if (!this.userSelectedTab) {
      // this.logger.info(
      //   'Auto-switching to preview view after code processing (user has not manually selected a tab)'
      // );
      this.togglePreviewView();
    } else {
      // this.logger.info(
      //   'Respecting user tab selection, not auto-switching to preview after code processing'
      // );
      // Still need to update the tab states without changing the view using BehaviorSubjects
      this.isCodeActive$.next(false);
      this.isPreviewActive$.next(true);
      this.isLogsActive$.next(false);
    }

    // Force check if iframe exists after a short delay
    setTimeout(() => {
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      // this.logger.debug('Iframe check in processCodeFromService:', !!iframe);
      if (!iframe) {
        // this.logger.warn(
        //   'Iframe not found in DOM after code processing, forcing reload of preview view'
        // );
        // Force a refresh of the view only if we're in preview view
        if (this.currentView$.value === 'preview') {
          // this.logger.info('Forcing refresh of preview view');
          this.currentView$.next('editor');
          setTimeout(() => {
            this.currentView$.next('preview');
          }, 50);
        } else {
          // this.logger.info('Not forcing refresh since we are not in preview view');
        }
      }
    }, 200);

    // Only use deployed URL from the service - no hardcoded fallbacks
    if (this.codeSharingService.getDeployedUrl()) {
      this.deployedUrl$.next(this.codeSharingService.getDeployedUrl() ?? '');
    }
    // Remove hardcoded URL fallback - only use extracted URLs from polling response

    // Only enable preview tab if we have a valid URL
    this.isPreviewTabEnabled = !!this.deployedUrl$.value;

    // Only sanitize the URL if we have one
    if (this.deployedUrl$.value) {
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
      // this.logger.info('URL sanitized in processCodeFromService:', this.deployedUrl$.value);
    } else {
      this.urlSafe = undefined;
      // this.logger.info('No URL available - preview tab disabled until deployment completes');
    }

    // The generatedCode should now come from the AppStateService via the CodeSharingService
    // No need to manually check sessionStorage

    // Subscribe to deployed URL changes
    this.subscription?.add(
      this.codeSharingService.deployedUrl$.subscribe(url => {
        this.deployedUrl$.next(url ?? '');
        if (url) {
          this.isPreviewLoading$.next(false);
          this.previewIcon$.next('bi-eye');
          this.isPreviewTabEnabled = true; // Enable the preview tab
        }
      })
    );
  }

  toggleView() {
    if (!this.deployedUrl$.value && this.currentView$.value === 'editor') {
      // Start preview generation
      this.isPreviewLoading$.next(true);
      this.previewIcon$.next('bi-arrow-clockwise');
      this.previewError$.next(false);

      const generatedCode = this.codeSharingService.getGeneratedCode();
      if (generatedCode) {
        this.codeGenerationService.getPreviewDeployment(generatedCode).subscribe({
          next: response => {
            if (response.status === 'success' && response.deployedUrl) {
              this.codeSharingService.setDeployedUrl(response.deployedUrl);
              this.currentView$.next('preview');
              this.previewError$.next(false);
            } else {
              this.previewError$.next(true);
              this.currentView$.next('preview');
            }
          },
          error: error => {
            // this.logger.error('Preview deployment failed:', error);
            this.isPreviewLoading$.next(false);
            this.previewIcon$.next('bi-code-slash');
            this.previewError$.next(true);
            this.currentView$.next('preview');
          },
          complete: () => {
            this.isPreviewLoading$.next(false);
          },
        });
      }
    } else {
      // Toggle between existing views
      const newView = this.currentView$.value === 'editor' ? 'preview' : 'editor';
      this.currentView$.next(newView);
      this.previewIcon$.next(newView === 'editor' ? 'bi-code-slash' : 'bi-eye');
    }
  }

  /**
   * Handle window resize events to ensure split screen remains responsive
   */
  @HostListener('window:resize')
  onWindowResize() {
    // Only update if not currently resizing to avoid conflicts
    if (!this.isResizing$.value) {
      // Get DOM elements
      const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
      const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
      const container = document.querySelector('.awe-splitscreen') as HTMLElement;

      if (leftPanel && rightPanel && container && !this.isLeftPanelCollapsed$.value) {
        // Ensure percentages are maintained during window resize
        const containerWidth = container.offsetWidth;

        // Parse the percentage values (remove the % sign)
        const leftWidthStr = this.defaultLeftPanelWidth;
        const rightWidthStr = this.defaultRightPanelWidth;

        // Extract numeric values from percentage strings
        const leftWidthPercent = parseFloat(leftWidthStr);
        const rightWidthPercent = parseFloat(rightWidthStr);

        // Validate that we have percentage values
        if (!isNaN(leftWidthPercent) && !isNaN(rightWidthPercent)) {
          // Calculate minimum width in pixels
          const minWidthPx = parseInt(this.minWidth$.value || '300', 10);

          // Check if left panel would be smaller than minimum width
          if ((containerWidth * leftWidthPercent / 100) < minWidthPx) {
            // Adjust to maintain minimum width
            const newLeftPercentage = (minWidthPx / containerWidth * 100).toFixed(2) + '%';
            const newRightPercentage = ((containerWidth - minWidthPx) / containerWidth * 100).toFixed(2) + '%';

            leftPanel.style.width = newLeftPercentage;
            rightPanel.style.width = newRightPercentage;

            // Update stored values
            this.defaultLeftPanelWidth = newLeftPercentage;
            this.defaultRightPanelWidth = newRightPercentage;
          }

          // Check if right panel would be smaller than minimum width
          else if ((containerWidth * rightWidthPercent / 100) < minWidthPx) {
            // Adjust to maintain minimum width
            const newRightPercentage = (minWidthPx / containerWidth * 100).toFixed(2) + '%';
            const newLeftPercentage = ((containerWidth - minWidthPx) / containerWidth * 100).toFixed(2) + '%';

            leftPanel.style.width = newLeftPercentage;
            rightPanel.style.width = newRightPercentage;

            // Update stored values
            this.defaultLeftPanelWidth = newLeftPercentage;
            this.defaultRightPanelWidth = newRightPercentage;
          }
        }
      }
    }
  }

  ngOnDestroy() {

    // 1. Unsubscribe from all subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }

    // 2. Complete and clean up the destroy$ subject used with takeUntil
    this.destroy$.next();
    this.destroy$.complete();

    // 3. Clear all timers and intervals
    if (this.checkSessionStorageInterval) {
      clearInterval(this.checkSessionStorageInterval);
      this.checkSessionStorageInterval = null;
    }

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
      this.logStreamTimer = null;
    }

    if (this.autoSwitchToLogsTimer) {
      clearTimeout(this.autoSwitchToLogsTimer);
      this.autoSwitchToLogsTimer = null;
    }

    // 4. Clean up canvas auto-centering resources
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = null;
    }

    if (this.canvasResizeObserver) {
      this.canvasResizeObserver.disconnect();
      this.canvasResizeObserver = undefined;
    }

    // Always reset logs regardless of polling state
    this.pollingService.resetLogs();

    if (this.isPolling) {
      this.pollingService.stopPolling();
      this.isPolling = false;
    }
this.currentProgressState = '';
    this.lastProgressDescription = '';
    this.pollingStatus = 'PENDING';
    this.hasLogs = false;
    this.logMessages = [];
    this.formattedLogMessages = [];
    this.processedLogHashes.clear();
    this.expandedCodeLogs.clear();
    this.promptService.setImage(null);
    this.promptService.setPrompt('');
    this.promptSubmissionService.resetSubmissionState();

    this.codeSharingService.resetState();

    this.appStateService.updateProjectState({
      codeGenerated: false,
      generatedCode: null
    });

    this.resetComponentState();

    // 7. Clean up element selection mode if active
    if (this.isElementSelectionMode) {
      this.cleanupSelectionMode();
      this.isElementSelectionMode = false;
      this.clearSelectedElement();
    }

    // 8. Clear any references to DOM elements and clean up iframe
    try {
      // Clean up iframe if it exists
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (iframe && iframe.contentWindow) {
        // Try to clean up iframe content
        iframe.src = 'about:blank';
      }
    } catch (error) {
      // this.logger.error('Error cleaning up iframe:', error);
    }

    // Set URL safe to null to release any references
    this.urlSafe = undefined;

    // Reset the user selected tab flag
    this.userSelectedTab = false;


    // Clear the current project ID from the stepper state service
    this.stepperStateService.clearCurrentProjectId();

    // Remove any lingering event listeners or classes
    document.body.classList.remove('user-select-none');

    // Clean up any active resize operation
    if (this.isResizing$.value) {
      const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
      const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
      const resizer = document.querySelector('.resizer') as HTMLElement;
      const overlay = document.getElementById('resize-overlay');

      if (leftPanel) leftPanel.classList.remove('dragging');
      if (rightPanel) rightPanel.classList.remove('dragging');
      if (resizer) resizer.classList.remove('active');

      // Clean up overlay if it exists
      if (overlay) {
        document.body.removeChild(overlay);
      }

      // Clean up global classes
      document.body.classList.remove('user-select-none');
      document.documentElement.classList.remove('resizing-active');

      this.isResizing$.next(false);
    }

    // Clear typewriter timeouts
    this.clearAllTypewriterTimeouts();

    // Clear ALL loading nodes (comprehensive cleanup)
    this.clearAllLoadingNodes();

    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Clear all typewriter timeouts
   */
  private clearAllTypewriterTimeouts(): void {
    Object.values(this.typewriterTimeouts).forEach(timeout => clearTimeout(timeout));
    this.typewriterTimeouts = {};
  }

  /**
   * Start typewriter animation for an artifact
   * @param artifactName The name of the artifact to animate
   * @param content The full content to type
   */
  private startArtifactTypewriter(artifactName: string, content: string): void {
    // Clear any existing animation for this artifact
    if (this.typewriterTimeouts[artifactName]) {
      clearTimeout(this.typewriterTimeouts[artifactName]);
    }

    // Initialize typewriter state
    this.artifactTypewriterStates[artifactName] = {
      visibleContent: '',
      isTyping: true,
      fullContent: content
    };

    // Start the typewriter animation
    this.typeArtifactCharacter(artifactName, 0);
  }

  /**
   * Type a single character for an artifact
   * @param artifactName The name of the artifact
   * @param charIndex The current character index
   */
  private typeArtifactCharacter(artifactName: string, charIndex: number): void {
    const state = this.artifactTypewriterStates[artifactName];
    if (!state || !state.isTyping) {
      return;
    }

    const fullContent = state.fullContent;
    if (charIndex < fullContent.length) {
      const nextChar = fullContent.charAt(charIndex);
      state.visibleContent = fullContent.substring(0, charIndex + 1);

      // Calculate typing delay with natural pauses
      let delay = this.artifactTypingSpeed;
      if (['.', '!', '?'].includes(nextChar)) {
        delay = this.artifactTypingSpeed * 1.3;
      } else if ([',', ';', ':'].includes(nextChar)) {
        delay = this.artifactTypingSpeed * 1.1;
      } else if (nextChar === ' ') {
        delay = this.artifactTypingSpeed * 0.7;
      } else if (nextChar === '\n') {
        delay = this.artifactTypingSpeed * 1.5;
      }

      // Trigger change detection for progressive markdown rendering
      this.cdr.detectChanges();

      // Schedule next character
      this.typewriterTimeouts[artifactName] = setTimeout(() => {
        this.typeArtifactCharacter(artifactName, charIndex + 1);
      }, delay);
    } else {
      // Animation complete
      state.isTyping = false;
      this.cdr.detectChanges();
    }
  }

  /**
   * Get the visible content for an artifact (for typewriter effect)
   * @param artifactName The name of the artifact
   * @returns The visible content or full content if not typing
   */
  getArtifactVisibleContent(artifactName: string): string {
    const state = this.artifactTypewriterStates[artifactName];
    if (state && state.isTyping) {
      return state.visibleContent;
    }
    // Return full content if not typing or state doesn't exist
    return this.selectedArtifactFile?.content || '';
  }

  // Update polling status in the UI
  private updatePollingStatus(isPolling: boolean): void {
    this.isPolling = isPolling;
    if (isPolling) {
      // Only show loading view initially, but don't force it if user has switched to another view
      if (this.currentView$.value === 'loading') {
        this.isLoading$.next(true);
      }

      // Show toast notification when polling starts
      this.toastService.info('Starting code generation process...');

      // Clear the selected image data URI when code generation starts
      // This ensures the image is only shown for the first prompt
      if (this.selectedImageDataUri) {
        this.selectedImageDataUri = '';
      }
    } else {
    }
  }

  /**
   * Starts the resize operation for the split screen
   * Implements smooth dragging with text selection prevention
   * @param event The mouse event that triggered the resize
   */
  startResize(event: MouseEvent) {
    // Prevent default to avoid text selection
    event.preventDefault();

    // Set resizing flag
    this.isResizing$.next(true);

    // Get DOM elements
    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const resizer = document.querySelector('.resizer') as HTMLElement;
    const container = document.querySelector('.awe-splitscreen') as HTMLElement;
    const splitScreen = document.querySelector('.smooth-split-screen') as HTMLElement;

    if (!leftPanel || !rightPanel || !container || !resizer) {
      // this.logger.error('Required DOM elements not found for resize operation');
      return;
    }

    // Create a transparent overlay to capture mouse events during resize
    const overlay = document.createElement('div');
    overlay.id = 'resize-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      cursor: col-resize;
      background: transparent;
      pointer-events: auto;
    `;
    document.body.appendChild(overlay);

    // Add active class to resizer for visual feedback
    resizer.classList.add('active');

    // Add dragging class to panels to disable transitions during drag
    leftPanel.classList.add('dragging');
    rightPanel.classList.add('dragging');

    // Add resizing class to split screen to prevent text selection
    if (splitScreen) {
      splitScreen.classList.add('resizing');
    }

    // Add user-select-none class to body to prevent text selection during resize
    document.body.classList.add('user-select-none');

    // Add resizing class to html element for global text selection prevention
    document.documentElement.classList.add('resizing-active');

    // Store initial positions
    const initialX = event.clientX;
    const containerWidth = container.offsetWidth;
    const initialLeftWidth = leftPanel.offsetWidth;

    // Calculate min widths (use minWidth from component or default to 300px)
    const minWidth = parseInt(this.minWidth$.value || '300', 10);
    const maxLeftWidth = containerWidth - minWidth;
    const minLeftWidth = minWidth;

    // Track velocity for momentum effect
    let lastX = initialX;
    let velocity = 0;
    let lastUpdateTime = Date.now();

    // Track animation frame for smoother performance
    let animationFrameId: number | null = null;

    // Create mousemove handler with requestAnimationFrame for smoother performance
    const handleMouseMove = (e: MouseEvent) => {
      // Prevent default and stop propagation to avoid text selection
      e.preventDefault();
      e.stopPropagation();

      // Cancel any pending animation frame
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
      }

      // Use requestAnimationFrame for smoother updates
      animationFrameId = requestAnimationFrame(() => {
        // Calculate how far the mouse has moved
        const dx = e.clientX - initialX;

        // Calculate new width as a percentage of container
        let newLeftWidth = initialLeftWidth + dx;

        // Apply constraints
        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        // Calculate percentage values
        const leftPercentage = (newLeftWidth / containerWidth * 100).toFixed(2) + '%';
        const rightPercentage = ((containerWidth - newLeftWidth) / containerWidth * 100).toFixed(2) + '%';

        // Apply new widths with hardware acceleration
        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        // Store the new widths as defaults for when panel is toggled
        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;

        // Calculate velocity for momentum effect
        const now = Date.now();
        const elapsed = now - lastUpdateTime;
        if (elapsed > 0) {
          velocity = (e.clientX - lastX) / elapsed;
          lastX = e.clientX;
          lastUpdateTime = now;
        }

        // Force change detection to update the UI
        this.ngZone.run(() => {
          this.cdr.detectChanges();
        });

        // Reset animation frame ID
        animationFrameId = null;
      });
    };

    // Create mouseup handler
    const handleMouseUp = () => {
      // Cancel any pending animation frame
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }

      // Remove the overlay and its event listeners
      const overlay = document.getElementById('resize-overlay');
      if (overlay) {
        overlay.removeEventListener('mousemove', handleMouseMove);
        overlay.removeEventListener('mouseup', handleMouseUp);
        overlay.removeEventListener('touchmove', handleTouchMove);
        overlay.removeEventListener('touchend', handleTouchEnd);
        document.body.removeChild(overlay);
      }

      // Remove document event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove, { passive: false } as EventListenerOptions);
      document.removeEventListener('touchend', handleTouchEnd);

      // Apply momentum effect if velocity is significant
      if (Math.abs(velocity) > 0.5) {
        const momentum = velocity * 10; // Adjust this multiplier for momentum strength
        let newLeftWidth = leftPanel.offsetWidth + momentum;

        // Apply constraints
        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        // Calculate percentage values
        const leftPercentage = (newLeftWidth / containerWidth * 100).toFixed(2) + '%';
        const rightPercentage = ((containerWidth - newLeftWidth) / containerWidth * 100).toFixed(2) + '%';

        // Apply momentum with smooth transition
        leftPanel.classList.remove('dragging');
        rightPanel.classList.remove('dragging');

        // Apply new widths
        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        // Store the new widths
        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;
      } else {
        // Remove dragging class to re-enable transitions
        leftPanel.classList.remove('dragging');
        rightPanel.classList.remove('dragging');
      }

      // Reset state
      this.isResizing$.next(false);

      // Remove active class from resizer
      resizer.classList.remove('active');

      // Remove resizing class from split screen
      if (splitScreen) {
        splitScreen.classList.remove('resizing');
      }

      // Remove user-select-none class from body
      document.body.classList.remove('user-select-none');

      // Remove resizing class from html element
      document.documentElement.classList.remove('resizing-active');

      // Force change detection to update the UI
      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });
    };

    // Touch support for mobile devices with improved performance
    const handleTouchMove = (e: TouchEvent) => {
      // Prevent scrolling during resize
      e.preventDefault();

      if (e.touches.length > 0) {
        const touchEvent = e.touches[0];

        // Create a synthetic mouse event
        const mouseEvent = {
          clientX: touchEvent.clientX,
          preventDefault: () => e.preventDefault()
        } as MouseEvent;

        // Use the same animation frame-based handler for touch events
        handleMouseMove(mouseEvent);
      }
    };

    const handleTouchEnd = () => {
      handleMouseUp();
    };

    // Add event listeners to the overlay for better mouse capture
    overlay.addEventListener('mousemove', handleMouseMove, { passive: false });
    overlay.addEventListener('mouseup', handleMouseUp);
    overlay.addEventListener('touchmove', handleTouchMove, { passive: false } as EventListenerOptions);
    overlay.addEventListener('touchend', handleTouchEnd);

    // Also add to document as fallback
    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchmove', handleTouchMove, { passive: false } as EventListenerOptions);
    document.addEventListener('touchend', handleTouchEnd);
  }
  onPanelToggled(isCollapsed: boolean) {
    this.isPanelCollapsed$.next(isCollapsed);
  }

  onFileChanged(_files: { filename: string; filecontent: string }[]) {
    // Placeholder for future implementation
  }

  onMessageReceived(_message: { type: string; data: any }) {
    // Placeholder for future implementation
  }

  onEditorReady(_vm: any) {
    this.editorReady = true;
    this.updateEditorWithGeneratedCode();
  }

  private updateEditorWithGeneratedCode() {
    try {

    } catch (error) {

    }
  }

  toggleHistoryView(): void {
    this.isHistoryActive$.next(!this.isHistoryActive$.value);
  }

  toggleCodeView(): void {
    // Only allow switching to code view if code is generated and there's no error
    if (!this.isCodeGenerationComplete) {
      // this.logger.warn('Cannot switch to code view - code not yet generated');
      return;
    }

    // Don't allow switching to code view if there's an error
    if (this.previewError$.value) {
      // this.logger.warn('Cannot switch to code view - preview has an error');
      return;
    }

    // this.logger.info('Switching to code view');

    // Save current content before switching tabs
    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states using BehaviorSubjects
    this.isCodeActive$.next(true);
    this.isPreviewActive$.next(false);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(false);

    // Set the current view to editor
    this.currentView$.next('editor');

    // Update UI state using BehaviorSubjects
    this.isLoading$.next(false);
    this.previewIcon$.next('bi-code-slash');

    // View will update automatically via reactive patterns
  }
  togglePreviewView(): void {
    // Save current content before switching tabs
    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(false);

    // Set the current view to preview
    this.currentView$.next('preview');

    // View will update automatically via reactive patterns

    // Azure URL generation removed - using Netlify URLs from deployment response

    // **PRIORITY CHECK**: Only re-sanitize legacy URLs if no new polling response URL is active
    // this.logger.info('🔍 URL re-sanitization check:', {
    //   deployedUrl: this.deployedUrl$.value,
    //   hasNewPollingResponseUrl: this.hasNewPollingResponseUrl,
    //   currentUrlSafe: this.urlSafe ? 'SET' : 'NOT_SET',
    //   newPreviewUrl: this.newPreviewUrl,
    //   isCodeGenerationComplete: this.isCodeGenerationComplete
    // });

    if (!this.hasNewPollingResponseUrl) {
      if (this.deployedUrl$.value) {
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
        // this.logger.info('🔧 Legacy URL re-sanitized for iframe (no polling response URL active)');
      } else {
        // If deployedUrl is null, use an empty string
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl('');
      }
    } else {
      // this.logger.info('🚫 Legacy URL re-sanitization skipped - new polling response URL is active');
      // this.logger.info('🎯 Keeping existing ref_code URL:', this.newPreviewUrl);
    }

    // Always hide loading spinner when code generation is complete and there's no error
    if (this.isCodeGenerationComplete && !this.previewError$.value) {
      this.isPreviewLoading$.next(false);

      // Force check if iframe exists
      setTimeout(() => {
        const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
        // this.logger.debug('Iframe check:', !!iframe);
        if (!iframe) {
          // this.logger.warn('Iframe not found in DOM, forcing reload of preview view');
          // Force a refresh of the view only if we're in preview view
          if (this.currentView$.value === 'preview') {
            // this.logger.info('Forcing refresh of preview view');
            // Temporarily switch to another view and back to force iframe recreation
            const currentView = this.currentView$.value;
            this.currentView$.next('editor');
            setTimeout(() => {
              this.currentView$.next(currentView);
            }, 50);
          } else {
            // this.logger.info('Not forcing refresh since we are not in preview view');
          }
        }
      }, 100);
    }
    // Hide loading spinner when we have a layout analyzed state AND valid layout data
    else if (
      this.currentProgressState &&
      (this.currentProgressState.includes('LAYOUT_ANALYZED') ||
        this.currentProgressState.includes('PAGES_GENERATED')) &&
      this.layoutData &&
      this.layoutData.length > 0 &&
      this.layoutMapping[this.layoutData[0]]
    ) {
      this.isPreviewLoading$.next(false);

      // Ensure layout data is available, contains only one key, and the key is valid
      if (
        this.currentProgressState.includes('LAYOUT_ANALYZED') ||
        this.currentProgressState.includes('PAGES_GENERATED')
      ) {
        // Only process layout data if we're not in the completed state
        if (!this.isCodeGenerationComplete) {
          if (!this.layoutData || this.layoutData.length === 0) {
          } else if (this.layoutData.length > 1) {
            // Ensure we only use the first layout key
            // Also verify the key is valid
            if (this.layoutMapping[this.layoutData[0]]) {
              this.layoutData = [this.layoutData[0]];
            } else {
              // If the first key is not valid, try to find a valid one
              const validKeys = this.layoutData.filter(key => this.layoutMapping[key]);
              if (validKeys.length > 0) {
                this.layoutData = [validKeys[0]];
              } else {
                // If no valid keys, don't use default fallback
                this.layoutData = [];
              }
            }
          } else {
            // We have exactly one key, but verify it's valid
            if (!this.layoutMapping[this.layoutData[0]]) {
              // If the key is not valid, don't use default fallback
              this.layoutData = [];
            }
          }
        } else {
          // If we're in the completed state, ensure layoutData is empty
          if (this.layoutData.length > 0) {
            this.layoutData = [];
          }
        }
      }

      // Make sure loading is disabled immediately
      this.isLayoutLoading = false;
    } else {
      // Show loading until we reach LAYOUT_ANALYZED state
      this.isPreviewLoading$.next(true);
    }
    this.previewIcon$.next('bi-eye');

    // Always enable the preview tab
    this.isPreviewTabEnabled = true;
  }

  toggleLogsView(): void {

    // Only proceed if logs are available
    if (!this.hasLogs) {
      return;
    }

    // Save current content before switching tabs
    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states - keep the current view but set logs active flag using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isLogsActive$.next(true);
    this.isArtifactsActive$.next(false);

    // Set the current view to logs
    this.currentView$.next('logs');

    // Don't show loading spinner, we'll show loading animation in the view if no logs
    this.isLoading$.next(false);

    // FIXED: Don't re-process logs when switching tabs
    // The logs are already processed and stored in formattedLogMessages
    // Just trigger change detection and scroll to bottom if we have formatted logs
    if (this.formattedLogMessages.length > 0) {
      // Trigger change detection to ensure the view updates
      this.cdr.detectChanges();

      // Scroll to bottom to show latest logs
      this.scrollLogsToBottom();
    }

    // View will update automatically via reactive patterns
  }

  toggleArtifactsView(): void {
    this.logger.debug('🎯 Artifacts tab toggle requested:', {
      isArtifactsTabEnabled: this.isArtifactsTabEnabled,
      artifactsDataLength: this.artifactsData.length,
      selectedArtifactFile: this.selectedArtifactFile?.name,
      loadedArtifacts: Array.from(this.loadedArtifacts),
      hasLayoutAnalyzed: this.hasLayoutAnalyzed,
      hasDesignSystem: this.hasDesignSystem
    });

    // Only proceed if artifacts tab is enabled
    if (!this.isArtifactsTabEnabled) {
      this.logger.warn('Artifacts tab is disabled, showing info toast');
      this.toastService.info('Artifacts will be available when generated during the workflow');
      return;
    }

    // Save current content before switching tabs
    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(true);

    // Set the current view to artifacts
    this.currentView$.next('artifacts');

    // Ensure all loaded artifacts are still in the artifacts data
    this.ensureArtifactPersistence();

    // If no artifact file is selected, select the first one
    if (!this.selectedArtifactFile && this.artifactsData.length > 0) {
      this.selectArtifactFile(this.artifactsData[0]);
    }

    // Force change detection to ensure view updates
    this.cdr.detectChanges();
  }

  /**
   * Toggle overview view for UI Design mode
   */
  toggleOverviewView(): void {
    // Save current content before switching tabs
    if (this.codeViewer && this.codeViewer.saveCurrentContent) {
      this.codeViewer.saveCurrentContent();
    }

    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(false);

    // Set the current view to overview
    this.currentView$.next('overview');

    // Force change detection to ensure view updates
    this.cdr.detectChanges();
  }

  /**
   * Handle page change in mobile frame
   */
  onUIDesignPageChange(pageIndex: number): void {
    this.currentUIDesignPageIndex$.next(pageIndex);
    this.logger.info('UI Design page changed to index:', pageIndex);
  }

  /**
   * Handle fullscreen request from mobile frame
   */
  onUIDesignFullscreenRequest(page: MobilePage): void {
    this.logger.info('Fullscreen requested for page:', page.fileName);
    this.createMobileFullscreenOverlay(page);
  }

  /**
   * Create a full-screen overlay for mobile content with proper scroll lock
   */
  private createMobileFullscreenOverlay(page: MobilePage): void {
    // Save original state for scroll restoration
    const originalBodyOverflow = document.body.style.overflow;
    const originalBodyPosition = document.body.style.position;
    const originalBodyTop = document.body.style.top;
    const originalBodyWidth = document.body.style.width;
    const scrollY = window.scrollY;
    // Create overlay container
    const overlay = document.createElement('div');
    overlay.className = 'mobile-fullscreen-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(8px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      animation: fadeIn 0.3s ease-out;
    `;

    // Create content container
    const content = document.createElement('div');
    content.className = 'overlay-content';

    // Responsive sizing based on viewport
    const isMobile = window.innerWidth <= 768;
    const contentWidth = isMobile ? '95vw' : '90vw';
    const contentHeight = isMobile ? '95vh' : '90vh';
    const maxWidth = isMobile ? 'none' : '420px';
    const maxHeight = isMobile ? 'none' : '720px';
    const borderRadius = isMobile ? '8px' : '12px';

    content.style.cssText = `
      position: relative;
      width: ${contentWidth};
      height: ${contentHeight};
      max-width: ${maxWidth};
      max-height: ${maxHeight};
      background: white;
      border-radius: ${borderRadius};
      overflow: hidden;
      animation: scaleIn 0.3s ease-out;
      box-shadow:
        0 0 0 1px rgba(255, 255, 255, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.2);
    `;

    // Create close button - positioned outside content for better visibility
    const closeButton = document.createElement('button');
    closeButton.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
    closeButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 48px;
      height: 48px;
      background: rgba(0, 0, 0, 0.8);
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10001;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(12px);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2);
      font-size: 0;
    `;

    // Add hover effects
    closeButton.addEventListener('mouseenter', () => {
      closeButton.style.background = 'rgba(220, 38, 38, 0.9)';
      closeButton.style.borderColor = 'rgba(255, 255, 255, 0.4)';
      closeButton.style.transform = 'scale(1.1)';
      closeButton.style.boxShadow = `
        0 6px 20px rgba(220, 38, 38, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3)
      `;
    });

    closeButton.addEventListener('mouseleave', () => {
      closeButton.style.background = 'rgba(0, 0, 0, 0.8)';
      closeButton.style.borderColor = 'rgba(255, 255, 255, 0.2)';
      closeButton.style.transform = 'scale(1)';
      closeButton.style.boxShadow = `
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2)
      `;
    });

    // Create iframe
    const iframe = document.createElement('iframe');
    iframe.srcdoc = page.content;
    iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      background: white;
      border-radius: 12px;
    `;
    iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-top-navigation-by-user-activation');

    // Add CSS animations and responsive styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; visibility: hidden; }
        to { opacity: 1; visibility: visible; }
      }
      @keyframes scaleIn {
        from { transform: scale(0.9); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }

      /* Responsive close button */
      @media (max-width: 768px) {
        .mobile-overlay-close-btn {
          top: 15px !important;
          right: 15px !important;
          width: 44px !important;
          height: 44px !important;
        }
      }

      @media (max-width: 480px) {
        .mobile-overlay-close-btn {
          top: 10px !important;
          right: 10px !important;
          width: 40px !important;
          height: 40px !important;
        }
      }
    `;
    document.head.appendChild(style);

    // Add responsive class to close button
    closeButton.classList.add('mobile-overlay-close-btn');

    // Close overlay function with scroll restoration
    const closeOverlay = () => {
      // Restore original body styles
      document.body.style.position = originalBodyPosition;
      document.body.style.top = originalBodyTop;
      document.body.style.width = originalBodyWidth;
      document.body.style.overflow = originalBodyOverflow;

      // Restore scroll position
      window.scrollTo(0, scrollY);

      // Remove elements
      if (document.body.contains(overlay)) {
        document.body.removeChild(overlay);
      }
      if (document.body.contains(closeButton)) {
        document.body.removeChild(closeButton);
      }
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };

    // Event listeners
    closeButton.addEventListener('click', closeOverlay);
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        closeOverlay();
      }
    });

    // Escape key listener
    const escapeListener = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeOverlay();
        document.removeEventListener('keydown', escapeListener);
      }
    };
    document.addEventListener('keydown', escapeListener);

    // Prevent content click from closing overlay
    content.addEventListener('click', (e) => {
      e.stopPropagation();
    });

    // Assemble overlay
    content.appendChild(iframe);
    overlay.appendChild(content);

    // Add close button directly to body for better positioning
    document.body.appendChild(closeButton);

    // Apply scroll lock
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';

    // Add to body
    document.body.appendChild(overlay);
  }



  /**
   * Ensure all loaded artifacts persist in the artifacts data
   * This method maintains artifact persistence across tab switches
   */
  private ensureArtifactPersistence(): void {
    // Check if we need to restore any missing artifacts
    this.loadedArtifacts.forEach(artifactName => {
      const exists = this.artifactsData.some(item => item.name === artifactName);
      if (!exists) {
        this.logger.info('Restoring missing artifact:', artifactName);
        this.restoreArtifact(artifactName);
      }
    });

    // Ensure layout analyzed artifact if flag is set
    if (this.hasLayoutAnalyzed && !this.artifactsData.some(item => item.name === 'Layout Analyzed')) {
      this.ensureLayoutAnalyzedFileExists();
    }

    // COMMENTED OUT: Ensure design system artifact if flag is set
    if (this.hasDesignSystem && !this.artifactsData.some(item => item.name === 'Design System')) {
      this.captureDesignSystemMappedState();
    }
  }

  /**
   * Restore a missing artifact based on its name
   */
  private restoreArtifact(artifactName: string): void {
    switch (artifactName) {
      case 'Project Overview':
        this.artifactsData.unshift({
          name: 'Project Overview',
          type: 'markdown',
          content: '# Project Overview\n\nProject overview content has been loaded.'
        });
        break;
      case 'Layout Analyzed':
        this.ensureLayoutAnalyzedFileExists();
        break;
      case 'Design System':
        // COMMENTED OUT: Restore design system artifact
        // this.captureDesignSystemMappedState();
        this.logger.info('Design System artifact restoration temporarily disabled');
        break;
      default:
        this.logger.warn('Unknown artifact to restore:', artifactName);
    }
  }

  /**
   * Selects an artifact file to display its content
   * @param file The artifact file to select
   */
  selectArtifactFile(file: any): void {
    // this.logger.info('Selecting artifact file:', file);
    this.selectedArtifactFile = file;

    // If the selected file is the Design System, initialize the design tokens
    if (file && file.name === 'Design System' && file.type === 'component') {
      this.initializeDesignTokens();
    }

    // If the selected file is Layout Analyzed, make sure we have layout data
    if (file && file.name === 'Layout Analyzed' && file.type === 'image') {
      // this.logger.info('Selected Layout Analyzed file, current layout data:', this.layoutData);
    }

    // Start typewriter animation for Project Overview markdown content only if not already completed
    if (file && file.name === 'Project Overview' && file.type === 'markdown' && file.content) {
      const existingState = this.artifactTypewriterStates[file.name];
      // Only start typewriter if it hasn't been started or is incomplete
      if (!existingState || existingState.isTyping || existingState.visibleContent.length < file.content.length) {
        this.startArtifactTypewriter(file.name, file.content);
      }
    }

    this.cdr.detectChanges();
  }

  /**
   * Initialize layout analyzed data from the layout data
   * This method creates layout data objects for display in the artifacts tab
   */
  private initializeLayoutAnalyzedData(): void {
    // Clear existing layout analyzed data
    this.layoutAnalyzedData = [];

    // If we have layout data, use it to create layout analyzed data
    if (this.layoutData && this.layoutData.length > 0) {
      // Filter out any invalid layout keys
      const validLayoutKeys = this.layoutData.filter(key => this.layoutMapping[key]);

      if (validLayoutKeys.length > 0) {
        // Create layout analyzed data objects for each valid layout key
        this.layoutAnalyzedData = validLayoutKeys.map(key => ({
          key,
          name: this.layoutMapping[key] || 'Identified Layout',
          imageUrl: `assets/images/layout-${key}.png`
        }));

        // this.logger.info('Initialized layout analyzed data with valid keys:', this.layoutAnalyzedData);
      } else {
        // If no valid layout keys, use a default layout
        this.layoutAnalyzedData = [{
          key: 'HB',
          name: 'Default Layout',
          imageUrl: 'assets/images/layout-HB.png'
        }];

        // this.logger.info('No valid layout keys found, using default layout');
      }
    } else {
      // If no layout data is available, use a default layout
      this.layoutAnalyzedData = [{
        key: 'HB',
        name: 'Default Layout',
        imageUrl: 'assets/images/layout-HB.png'
      }];

      // this.logger.info('No layout data available, using default layout');
    }

    // Always set the flag that layout analyzed data is available
    // This ensures the artifact is always shown once it's been detected
    if (this.hasLayoutAnalyzed) {
      // Ensure the Layout Analyzed file exists in artifacts data
      this.ensureLayoutAnalyzedFileExists();
    }

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  /**
   * Remove Layout Analyzed from artifacts data if it exists
   */
  private removeLayoutAnalyzedFromArtifacts(): void {
    const layoutAnalyzedIndex = this.artifactsData.findIndex(file => file.name === 'Layout Analyzed');
    if (layoutAnalyzedIndex !== -1) {
      this.artifactsData.splice(layoutAnalyzedIndex, 1);
      // this.logger.info('Removed Layout Analyzed from artifacts data');

      // If the removed file was selected, select the first available file
      if (this.selectedArtifactFile && this.selectedArtifactFile.name === 'Layout Analyzed') {
        if (this.artifactsData.length > 0) {
          this.selectArtifactFile(this.artifactsData[0]);
        } else {
          this.selectedArtifactFile = null;
        }
      }
    }
  }

  /**
   * Ensure the Layout Analyzed file exists in the artifacts data
   * If it doesn't exist, add it with the appropriate image URL
   * If it does exist, update its content with the latest layout data
   */
  private ensureLayoutAnalyzedFileExists(): void {
    // Get the first layout image URL or use default
    let layoutImageUrl = 'assets/images/layout-HB.png'; // Default to HB layout
    if (this.layoutData && this.layoutData.length > 0) {
      const layoutKey = this.layoutData[0];
      layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    } else if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      layoutImageUrl = this.layoutAnalyzedData[0].imageUrl;
    }

    // Check if the Layout Analyzed file already exists
    const layoutAnalyzedIndex = this.artifactsData.findIndex(file => file.name === 'Layout Analyzed');

    if (layoutAnalyzedIndex === -1) {
      // If it doesn't exist, add it to artifacts data
      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl
      });

      // this.logger.info('Added Layout Analyzed to artifacts data with image URL:', layoutImageUrl);
    } else {
      // If it exists, update its content with the latest layout data
      this.artifactsData[layoutAnalyzedIndex].content = layoutImageUrl;
      // this.logger.info('Updated Layout Analyzed in artifacts data with image URL:', layoutImageUrl);
    }
  }

  /**
   * Capture the layout analyzed state from the polling response
   * This method ensures the layout analyzed data is permanently stored in artifacts
   */
  private captureLayoutAnalyzedState(): void {
    // Set the flag that layout analyzed data is available
    this.hasLayoutAnalyzed = true;

    // Initialize layout analyzed data
    this.initializeLayoutAnalyzedData();

    // Ensure the Layout Analyzed file exists in artifacts data
    // Get the first layout image URL or use default
    let layoutImageUrl = 'assets/images/layout-HB.png'; // Default to HB layout
    if (this.layoutData && this.layoutData.length > 0) {
      const layoutKey = this.layoutData[0];
      layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    } else if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      layoutImageUrl = this.layoutAnalyzedData[0].imageUrl;
    }

    // Check if the Layout Analyzed file already exists
    const layoutAnalyzedIndex = this.artifactsData.findIndex(file => file.name === 'Layout Analyzed');

    if (layoutAnalyzedIndex === -1) {
      // If it doesn't exist, add it to artifacts data
      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl
      });

      // this.logger.info('Added Layout Analyzed to artifacts data with image URL:', layoutImageUrl);
    } else {
      // If it exists, update its content with the latest layout data
      this.artifactsData[layoutAnalyzedIndex].content = layoutImageUrl;
      // this.logger.info('Updated Layout Analyzed in artifacts data with image URL:', layoutImageUrl);
    }

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  /**
   * Capture the design system mapped state from the polling response
   * This method ensures the design system data is permanently stored in artifacts
   */
  private captureDesignSystemMappedState(): void {
    // Set the flag that design system data is available
    this.hasDesignSystem = true;

    // Add Design System to artifacts data if not already present
    const hasDesignSystemFile = this.artifactsData.some(file => file.name === 'Design System');
    if (!hasDesignSystemFile) {
      // Create a placeholder for the design system component
      // The actual content will be rendered dynamically when the file is selected
      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: 'Design system information will be displayed here.'
      });

      // Initialize design tokens
      this.initializeDesignTokens();

      // this.logger.info('Added Design System to artifacts data');

      // If this is the first artifact, select it automatically
      if (this.artifactsData.length === 1) {
        this.selectArtifactFile(this.artifactsData[0]);
      }
    }

    // View will update automatically via reactive patterns
  }

  /**
   * Remove Design System from artifacts data if it exists
   */
  private removeDesignSystemFromArtifacts(): void {
    const designSystemIndex = this.artifactsData.findIndex(file => file.name === 'Design System');
    if (designSystemIndex !== -1) {
      this.artifactsData.splice(designSystemIndex, 1);
      // this.logger.info('Removed Design System from artifacts data');

      // If the removed file was selected, select the first available file
      if (this.selectedArtifactFile && this.selectedArtifactFile.name === 'Design System') {
        if (this.artifactsData.length > 0) {
          this.selectArtifactFile(this.artifactsData[0]);
        } else {
          this.selectedArtifactFile = null;
        }
      }
    }
  }

  /**
   * Initialize design tokens from the design system data
   * This method creates editable tokens for colors, typography, and buttons
   */
  private initializeDesignTokens(): void {
    // Use centralized design tokens data
    this.designTokens = [...ALL_DESIGN_TOKENS];

    // Log token statistics for debugging
    // this.logger.info('Design tokens initialized:', {
    //   total: DESIGN_TOKENS_STATS.total,
    //   colors: DESIGN_TOKENS_STATS.colors,
    //   typography: DESIGN_TOKENS_STATS.typography,
    //   buttons: DESIGN_TOKENS_STATS.buttons,
    //   categories: DESIGN_TOKENS_STATS.categories
    // });

    // If we have actual design system data from API response, merge it
    // if (this.designSystemData) {
    //   this.parseDesignSystemData();
    // }

    // View will update automatically via reactive patterns
  }

  /**
   * Add default color tokens when no design system data is available
   */
  // private addDefaultColorTokens(): void {
  //   const defaultColors: ColorToken[] = [
  //      { name: 'D_Yellow', value: '#E48900', hexCode: '#E48900' },
  //     { name: 'Lemon', value: '#FFA826', hexCode: '#FFA826' },
  //     { name: 'Black', value: '#212121', hexCode: '#212121' },
  //     { name: 'D_Grey', value: '#4D4D4D', hexCode: '#4D4D4D' },
  //     { name: 'Silver', value: '#F5F7FA', hexCode: '#F5F7FA' }
  //   ];

  //   // Add color tokens to the design tokens array
  //   defaultColors.forEach((color, index) => {
  //     this.designTokens.push({
  //       id: `color-${index}`,
  //       name: color.name,
  //       value: color.value,
  //       type: 'color',
  //       category: 'Colors',
  //       editable: true
  //     });
  //   });
  // }

  /**
   * Add default typography tokens when no design system data is available
   */
  // private addDefaultTypographyTokens(): void {
  //   const defaultFontStyles: FontStyleToken[] = [
  //     { name: 'Headline 1', size: '64/76', weight: 'Semi Bold', lineHeight: '1.2' },
  //     { name: 'Headline 2', size: '36/44', weight: 'Semi Bold', lineHeight: '1.2' },
  //     { name: 'Headline 3', size: '28/36', weight: 'Semi Bold', lineHeight: '1.3' },
  //     { name: 'Headline 4', size: '20/28', weight: 'Semi Bold', lineHeight: '1.4' }
  //   ];

  //   // Add typography tokens to the design tokens array
  //   defaultFontStyles.forEach((font, index) => {
  //     this.designTokens.push({
  //       id: `typography-${index}`,
  //       name: font.name,
  //       value: `${font.weight}, ${font.size}`,
  //       type: 'typography',
  //       category: 'Font Style Desktop',
  //       editable: true
  //     });
  //   });
  // }

  /**
   * Add default button tokens when no design system data is available
  //  */
  // private addDefaultButtonTokens(): void {
  //   const defaultButtons: ButtonToken[] = [
  //     { label: 'Label', variant: 'primary' },
  //     { label: 'Label', variant: 'primary', hasIcon: true },
  //     { label: 'Label', variant: 'secondary' },
  //     { label: 'Label', variant: 'outline' },
  //     { label: 'Label', variant: 'text' }
  //   ];

  //   // Add button tokens to the design tokens array
  //   defaultButtons.forEach((button, index) => {
  //     this.designTokens.push({
  //       id: `button-${index}`,
  //       name: button.label,
  //       value: button.variant,
  //       type: 'size',
  //       category: 'Buttons',
  //       editable: true
  //     });
  //   });
  // }
/**
   * Generate a color name based on a hex value
   * @param hexColor The hex color value
   * @returns A generated color name
   */
  generateColorName(hexColor: string): string {
    // Remove # if present
    const hex = hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;

    // Basic color mapping for common colors
    const colorMap: {[key: string]: string} = {
      '000000': 'Black',
      'FFFFFF': 'White',
      'FF0000': 'Red',
      '00FF00': 'Green',
      '0000FF': 'Blue',
      'FFFF00': 'Yellow',
      '00FFFF': 'Cyan',
      'FF00FF': 'Magenta',
      'C0C0C0': 'Silver',
      '808080': 'Gray',
      '800000': 'Maroon',
      '808000': 'Olive',
      '008000': 'Dark Green',
      '800080': 'Purple',
      '008080': 'Teal',
      '000080': 'Navy',
      'FFA500': 'Orange',
      'A52A2A': 'Brown',
      'FFC0CB': 'Pink',
      'E48900': 'D_Yellow',
      'FFA826': 'Lemon',
      '212121': 'Black',
      '4D4D4D': 'D_Grey',
      'F5F7FA': 'Silver'
    };

    // Check if the color is in our map
    if (colorMap[hex.toUpperCase()]) {
      return colorMap[hex.toUpperCase()];
    }

    // Parse the hex color to RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Determine the hue
    let hue = '';
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);

    if (max === min) {
      // It's a shade of gray
      const brightness = Math.round((r + g + b) / 3);
      if (brightness < 64) return 'Dark Gray';
      if (brightness < 128) return 'Gray';
      if (brightness < 192) return 'Light Gray';
      return 'Off White';
    }

    if (r === max) {
      if (g > b) hue = 'Orange';
      else hue = 'Red';
    } else if (g === max) {
      if (r > b) hue = 'Yellow Green';
      else hue = 'Green';
    } else {
      if (r > g) hue = 'Purple';
      else hue = 'Blue';
    }

    // Determine brightness
    const brightness = (r + g + b) / 3;
    let prefix = '';

    if (brightness < 85) prefix = 'Dark ';
    else if (brightness > 170) prefix = 'Light ';

    return prefix + hue;
  }
  /**
   * Parse design system data from API response
  //  */
  // private parseDesignSystemData(): void {
  //   // This would parse actual design system data from the API
  //   // For now, we'll use the default tokens
  //   this.addDefaultColorTokens();
  //   this.addDefaultTypographyTokens();
  //   this.addDefaultButtonTokens();
  // }

  /**
   * Update a design token value
   * @param tokenId The ID of the token to update
   * @param newValue The new value for the token
   */
  updateDesignToken(tokenId: string, newValue: string): void {
    // Find the token by ID
    const tokenIndex = this.designTokens.findIndex(token => token.id === tokenId);

    if (tokenIndex !== -1) {
      const token = this.designTokens[tokenIndex];

      // Update the token value
      token.value = newValue;

      // If it's a color token, update the name based on the color
      if (token.type === 'color') {
        token.name = this.generateColorName(newValue);
      }

      // Send updated tokens to backend when color values change
      this.sendDesignTokensToBackend();

      // View will update automatically via reactive patterns
    }
  }

  /**
   * Get all design tokens in a format suitable for backend communication
   * @returns Formatted design tokens object
   */
  getDesignTokensForBackend(): any {
    const tokensByCategory = {
      colors: this.designTokens
        .filter(token => token.type === 'color')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          hexCode: token.value,
          category: token.category,
          editable: token.editable
        })),
      typography: this.designTokens
        .filter(token => token.type === 'typography')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          category: token.category,
          editable: token.editable
        })),
      buttons: this.designTokens
        .filter(token => token.category === 'Buttons')
        .map(token => ({
          id: token.id,
          name: token.name,
          variant: token.value,
          category: token.category,
          editable: token.editable
        }))
    };

    return {
      designSystem: {
        tokens: tokensByCategory,
        timestamp: new Date().toISOString(),
        projectId: this.projectId,
        jobId: this.jobId
      }
    };
  }

  // Debounce timer for design tokens updates
  private designTokensUpdateTimer: any;

  /**
   * Send design tokens to backend for processing (with debouncing)
   */
  private sendDesignTokensToBackend(): void {
    if (!this.projectId || !this.jobId) {
      // this.logger.warn('Cannot send design tokens: missing project ID or job ID');
      return;
    }

    // Clear existing timer to debounce rapid changes
    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
    }

    // Set a new timer to send the update after 1 second of no changes
    this.designTokensUpdateTimer = setTimeout(() => {
      const tokensPayload = this.getDesignTokensForBackend();

      // this.logger.info('Sending design tokens to backend:', tokensPayload);

      // Send the tokens update request
      this.sendTokensUpdateRequest(tokensPayload);
    }, 1000); // 1 second debounce
  }

  /**
   * Send tokens update request to backend
   * @param tokensPayload The design tokens payload
   */
  private sendTokensUpdateRequest(tokensPayload: any): void {
    // Create the request payload for design tokens update
    const updateRequest = {
      project_id: this.projectId,
      job_id: this.jobId,
      action: 'update_design_tokens',
      design_tokens: tokensPayload.designSystem.tokens,
      timestamp: tokensPayload.designSystem.timestamp
    };

    // Send to backend using the code generation service
    this.codeGenerationService.updateDesignTokens(updateRequest).subscribe({
      next: (response) => {
        this.logger.info('Design tokens updated successfully:', response);
        this.toastService.success('Design tokens updated');
      },
      error: (error) => {
        this.logger.error('Failed to update design tokens:', error);
        this.toastService.error('Failed to update design tokens');
      }
    });
  }

  /**
   * Manually trigger design tokens update (useful for testing or immediate updates)
   */
  triggerDesignTokensUpdate(): void {
    // Clear any pending timer
    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
      this.designTokensUpdateTimer = null;
    }

    // Send immediately
    if (this.projectId && this.jobId) {
      const tokensPayload = this.getDesignTokensForBackend();
      this.logger.info('Manually triggering design tokens update:', tokensPayload);
      this.sendTokensUpdateRequest(tokensPayload);
    } else {
      this.logger.warn('Cannot trigger design tokens update: missing project ID or job ID');
    }
  }

  /**
   * Get the current state of design tokens for debugging or external use
   */
  getCurrentDesignTokensState(): any {
    return {
      tokens: this.designTokens,
      hasDesignSystem: this.hasDesignSystem,
      projectId: this.projectId,
      jobId: this.jobId,
      backendPayload: this.getDesignTokensForBackend()
    };
  }

  /**
   * Check if a string is a valid hex color
   * @param color The color string to validate
   * @returns True if the color is a valid hex color
   */
  isValidHexColor(color: string): boolean {
    // Use the centralized validation function
    return validateHexColor(color);
  }

  /**
   * Handle token value change from input element
   * @param event The change event
   * @param tokenId The ID of the token to update
   */
onTokenValueChange(event: Event, tokenId: string): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.value) {
      const value = inputElement.value;

      // For color tokens, ensure it's a valid hex color
      const token = this.designTokens.find(t => t.id === tokenId);
      if (token && token.type === 'color') {
        // Check if it's a valid hex color
        const isValid = this.isValidHexColor(value);

        // Add or remove the invalid class based on validation
        if (isValid) {
          inputElement.classList.remove('invalid');

          // Ensure the value has a # prefix
          const formattedValue = value.startsWith('#') ? value : `#${value}`;
          this.updateDesignToken(tokenId, formattedValue);
        } else {
          inputElement.classList.add('invalid');
          // Don't update the token if the value is invalid
          return;
        }
      } else {
        // For non-color tokens, update as is
        this.updateDesignToken(tokenId, value);
      }
    }
  }

  /**
   * Get design tokens filtered by category
   * @param category The category to filter by
   * @returns Filtered array of design tokens
   */
  getTokensByCategory(category: string): DesignToken[] {
    // Use the centralized helper function with current tokens
    return this.designTokens.filter(token => token.category === category);
  }

  /**
   * Get the file icon class based on file type
   * @param fileType The type of the file
   * @returns The CSS class for the file icon
   */
  getFileIconClass(fileType: string): string {
    switch (fileType) {
      case 'markdown':
        return 'file-icon-md';
      case 'image':
        return 'file-icon-img';
      case 'svg':
        return 'file-icon-svg';
      case 'text':
        return 'file-icon-txt';
      case 'component':
        return 'file-icon-component';
      default:
        return 'file-icon-default';
    }
  }

  // Index of the last displayed log message for streaming effect
  lastDisplayedLogIndex = 0;

  /**
   * Returns the appropriate CSS class for a log message based on its content
   * @param log The log message to analyze
   * @returns CSS class name for styling the log
   */
  getLogClass(log: string | any): string {
    // If the log is an object with a type property, use that
    if (typeof log === 'object' && log !== null && log.type) {
      switch (log.type) {
        case 'error':
          return 'log-error';
        case 'warning':
          return 'log-warning';
        case 'debug':
          return 'log-debug';
        case 'code':
          return 'log-code';
        case 'progress-description':
          return 'log-info progress-description';
        case 'status-change':
          return 'log-info status-change';
        default:
          return 'log-info';
      }
    }

    // Otherwise, analyze the string content
    if (typeof log === 'string') {
      if (log.includes('ERROR')) {
        return 'log-error';
      } else if (log.includes('WARN')) {
        return 'log-warning';
      } else if (log.includes('DEBUG')) {
        return 'log-debug';
      } else if (log.includes('Progress Description')) {
        // Highlight progress description logs
        return 'log-info progress-description';
      } else if (log.includes('Status changed to:')) {
        // Highlight status change logs
        return 'log-info status-change';
      } else {
        return 'log-info';
      }
    }

    return 'log-info';
  }

  @Input() defaultLeftPanelWidth: string = '50%';
  @Input() defaultRightPanelWidth: string = '50%';

  toggleLeftPanel(): void {
    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const splitScreen = document.querySelector('.awe-splitscreen') as HTMLElement;

    if (leftPanel && rightPanel && splitScreen) {
      if (this.isLeftPanelCollapsed$.value) {
        // Expand left panel (return to default position)
        leftPanel.style.width = this.defaultLeftPanelWidth;
        rightPanel.style.width = this.defaultRightPanelWidth;
        this.isLeftPanelCollapsed$.next(false);
        // Remove the class that hides the resizer
        splitScreen.classList.remove('left-panel-collapsed');
      } else {
        // Collapse left panel
        leftPanel.style.width = '0%';
        rightPanel.style.width = '100%';
        this.isLeftPanelCollapsed$.next(true);
        // Add the class to hide the resizer
        splitScreen.classList.add('left-panel-collapsed');
      }
    }
  }


  // Property to store the selected image data URI (already defined above)

  // Base icons for the chat window (without file attach)
  private baseRightIcons: { name: string; status: IconStatus }[] = [
    { name: 'awe_enhance', status: 'active' },
    { name: 'awe_enhanced_send', status: 'active' },
  ];

  // File attach icon (conditionally added)
  private fileAttachIcon: { name: string; status: IconStatus } = { name: 'awe_enhanced_alternate', status: 'default' };
  get rightIcons(): { name: string; status: IconStatus }[] {
    const isUIDesignMode = this.isUIDesignMode$.value;
    if (isUIDesignMode) {
      return [...this.baseRightIcons];
    } else {
      return [this.fileAttachIcon, ...this.baseRightIcons];
    }
  }

  // Update icons based on theme

  handleIconClick(event: { name: string; side: string; index: number; theme: string }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        this.handleEnhancedAlternate();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        if (event.theme === 'dark') {
          this.handleEnhancedSendDark();
        } else if (event.theme === 'light') {
          this.handleEnhancedSendLight();
        }
        break;
    }
  }

  handleEnhancedSendDark(): void {
    if (!this.darkPrompt.trim()) return;

    this.darkMessages.push({ text: this.darkPrompt, from: 'user', theme: 'dark' });

    setTimeout(() => {
      this.darkMessages.push({
        text: 'This is an AI-generated reply to your message (dark theme).',
        from: 'ai',
        theme: 'dark',
      });
    }, 100);

    this.darkPrompt = '';
  }

  // Flag to track if AI is responding
  isAiResponding: boolean = false;

  /**
   * Handles the send button click or Enter key press in the chat window
   * If an element is selected, it will send a modification request
   * If code generation is complete, it will handle edit requests
   * Otherwise, it will handle the message as a regular chat message
   */
  handleEnhancedSendLight(): void {
    if (!this.lightPrompt.trim()) return;
    if (this.isAiResponding) return; // Prevent sending while AI is responding

    // Check if we have a selected element and need to handle element modification
    if (this.selectedElementHTML && this.selectedElementPath) {
      // Handle element modification request
      this.sendElementModificationRequest(this.lightPrompt);

      // Clear the prompt
      this.lightPrompt = '';

      return;
    }

    // Check if code generation is complete and we should handle edit requests
    if (this.isCodeGenerationComplete) {
      this.handleCodeEditRequest();
      return;
    }

    // Store the current image data URI for this message
    const currentImageDataUri = this.selectedImageDataUri;

    // Regular message handling for initial generation
    this.isAiResponding = true; // Set flag to indicate AI is responding
    this.lightMessages.push({
      text: this.lightPrompt,
      from: 'user',
      theme: 'light',
      imageDataUri: currentImageDataUri || undefined
    });

    // Clear the image data URI after adding it to the message
    // This ensures it's only shown for the first prompt
    if (currentImageDataUri) {
      this.selectedImageDataUri = '';
    }

    // Clear the prompt input
    this.lightPrompt = '';

    // Add initial message
    setTimeout(() => {
      this.lightMessages.push({
        text: 'Analyzing your request...',
        from: 'ai',
        theme: 'light',
      });
    }, 500);

    // Get the current project state to include user selections
    this.appStateService.project$
      .subscribe(projectState => {
        // Create a summary of user selections
        let userSelectionsSummary = '';

        // Add image information if available
        if (projectState.imageUrl) {
          userSelectionsSummary += '- **Image**: You provided an image for reference\n';
        }

        // Add technology information if available
        if (projectState.technology) {
          userSelectionsSummary += `- **Technology**: ${projectState.technology.charAt(0).toUpperCase() + projectState.technology.slice(1)}\n`;
        }

        // Add design library information if available
        if (projectState.designLibrary) {
          userSelectionsSummary += `- **Design Library**: ${projectState.designLibrary.charAt(0).toUpperCase() + projectState.designLibrary.slice(1)}\n`;
        }

        // Add application type information if available
        if (projectState.application) {
          userSelectionsSummary += `- **Application Type**: ${projectState.application.charAt(0).toUpperCase() + projectState.application.slice(1)}\n`;
        }

        // Add type information if available
        if (projectState.type) {
          userSelectionsSummary += `- **Generation Type**: ${projectState.type}\n`;
        }

        // Comprehensive project overview in a single message
        setTimeout(() => {
          // Update the last message instead of adding a new one
          const lastMessage = this.lightMessages[this.lightMessages.length - 1];
          if (lastMessage && lastMessage.from === 'ai') {
            // Get the user's request from the previous message (for context)
            // const userPrompt = this.lightMessages[this.lightMessages.length - 2]?.text || 'your request';


          }
          this.isAiResponding = false; // Reset the flag when AI is done responding
        }, 2000);
      })
      .unsubscribe(); // Unsubscribe immediately since we only need the current value
  }

  /**
   * Handle code edit requests when code generation is complete
   * ENHANCED: Preview state management during regeneration
   */
  private handleCodeEditRequest(): void {
    const userRequest = this.lightPrompt.trim();
    const currentImageDataUri = this.selectedImageDataUri;

    // Add user message to chat
    this.lightMessages.push({
      text: userRequest,
      from: 'user',
      theme: 'light',
      imageDataUri: currentImageDataUri || undefined
    });

    // Clear the image data URI and prompt
    if (currentImageDataUri) {
      this.selectedImageDataUri = '';
    }
    this.lightPrompt = '';

    // Set AI responding state
    this.isAiResponding = true;

    // ENHANCED: Start regeneration preview loading state
    this.startRegenerationPreviewLoading();

    // Add initial AI response
    this.lightMessages.push({
      text: 'Processing your edit request...',
      from: 'ai',
      theme: 'light',
    });

    // Get current code files from Monaco editor
    const currentCodeFiles = this.getCurrentCodeFiles();

    if (!currentCodeFiles || currentCodeFiles.length === 0) {
      this.handleEditError('No code files found to edit. Please ensure code generation is complete.');
      // ENHANCED: Stop preview loading on error
      this.stopRegenerationPreviewLoading();
      return;
    }

    // Prepare images array (empty array if no image is provided)
    const images: string[] = currentImageDataUri ? [currentImageDataUri] : [];

    // Call the edit API
    this.codeGenerationService.editCode(currentCodeFiles, userRequest, images, this.projectId || undefined)
      .subscribe({
        next: (response) => {
          this.handleEditResponse(response);
        },
        error: (error) => {
          this.logger.error('Error during code edit:', error);
          this.handleEditError('Failed to process your edit request. Please try again.');
          // ENHANCED: Stop preview loading on error
          this.stopRegenerationPreviewLoading();
        }
      });
  }

  /**
   * Start regeneration preview loading state
   * ENHANCED: Manages preview state during code regeneration with proper loader display
   */
  private startRegenerationPreviewLoading(): void {
    this.logger.info('🔄 Starting regeneration preview loading state');

    // ENHANCED: Clear urlSafe to trigger deployment loading container
    // This ensures the loading animation shows during regeneration
    this.urlSafe = undefined;
    this.logger.info('🔗 Cleared urlSafe to trigger regeneration loading display');

    // Set preview loading state
    this.isPreviewLoading$.next(true);
    this.previewIcon$.next('bi-arrow-clockwise');
    this.previewError$.next(false);

    // Reset iframe validation states to trigger re-validation after regeneration
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);
    this.isIframeReady$.next(false);

    // Clear any validation errors
    this.urlValidationError$.next('');

    // Enable preview tab to show loading state
    this.isPreviewTabEnabled = true;

    // Switch to preview tab to show loading state
    if (this.currentView$.value !== 'preview') {
      this.currentView$.next('preview');
    }

    // Force change detection to ensure loading state is visible
    this.cdr.detectChanges();

    this.logger.info('✅ Regeneration preview loading state activated - loader should be visible');
  }

  /**
   * Stop regeneration preview loading state
   * ENHANCED: Cleans up loading state on error or completion
   */
  private stopRegenerationPreviewLoading(): void {
    this.logger.info('🛑 Stopping regeneration preview loading state');

    // Stop preview loading
    this.isPreviewLoading$.next(false);
    this.previewIcon$.next('bi-eye');

    // Clear regeneration state if still in progress
    if (this.isRegenerationInProgress$.value) {
      this.logger.info('🧹 Clearing regeneration state');
      this.isRegenerationInProgress$.next(false);
      this.regenerationStartTime = 0;
    }

    this.logger.info('✅ Regeneration preview loading state deactivated');
  }

  /**
   * Complete regeneration process and restore deployed URL
   * ENHANCED: Handles successful regeneration completion with preview restoration
   */
  private completeRegenerationProcess(): void {
    this.logger.info('🎯 Completing regeneration process');

    // Stop preview loading state
    this.stopRegenerationPreviewLoading();

    // The preview URL will be automatically updated when the polling service
    // detects DEPLOY + COMPLETED state and the new polling response processor
    // extracts the URL from ref_code metadata
    this.logger.info('🔄 Waiting for new deployment URL from polling service...');

    // Force change detection to ensure UI updates
    this.cdr.detectChanges();

    // Get the current deployed URL to potentially restore
    const currentDeployedUrl = this.deployedUrl$.value;

    if (currentDeployedUrl) {
      this.logger.info('🔄 Current deployed URL available:', currentDeployedUrl);

      // Enable preview tab
      this.isPreviewTabEnabled = true;
      this.previewError$.next(false);

      this.logger.info('✅ Deployed URL maintained successfully');
    } else {
      this.logger.info('ℹ️ No deployed URL available - waiting for new deployment');

      // Keep loading state active until new deployment URL is received
      // The preview loading will be stopped when the new URL is processed
      this.logger.info('⏳ Keeping preview in loading state until new deployment completes');
    }

    // Ensure we're on the preview tab to show the result
    if (this.currentView$.value !== 'preview') {
      this.currentView$.next('preview');
    }

    this.logger.info('🎉 Regeneration process completed successfully');
  }

  /**
   * Complete regeneration process after deployment is ready
   * ENHANCED: Handles regeneration completion when deployment is updated with new content
   */
  private completeRegenerationAfterDeployment(): void {
    this.logger.info('🎯 Completing regeneration after deployment is ready');

    // Calculate regeneration duration
    const regenerationDuration = Date.now() - this.regenerationStartTime;
    this.logger.info(`⏱️ Regeneration completed in ${regenerationDuration}ms`);

    // Clear regeneration state
    this.isRegenerationInProgress$.next(false);
    this.regenerationStartTime = 0;

    // Stop preview loading state
    this.stopRegenerationPreviewLoading();

    // Enable preview tab and clear errors
    this.isPreviewTabEnabled = true;
    this.previewError$.next(false);

    // ENHANCED: Ensure iframe is properly loaded after regeneration
    this.ensureIframeLoadedAfterRegeneration();

    this.logger.info('✅ Regeneration process completed successfully with updated deployment');
  }

  /**
   * Handle regeneration failure scenarios
   * ENHANCED: Ensures proper iframe state management when regeneration fails
   */
  private handleRegenerationFailure(): void {
    this.logger.info('🚨 Handling regeneration failure');

    // Clear regeneration state
    this.isRegenerationInProgress$.next(false);
    this.regenerationStartTime = 0;

    // Stop preview loading state
    this.stopRegenerationPreviewLoading();

    // Check if we have a previous deployed URL to fall back to
    const currentUrl = this.deployedUrl$.value;
    if (currentUrl && currentUrl !== 'ERROR_DEPLOYMENT_FAILED') {
      this.logger.info('🔄 Regeneration failed - restoring previous deployed URL in iframe');

      // Enable preview tab and clear errors
      this.isPreviewTabEnabled = true;
      this.previewError$.next(false);

      // Ensure iframe is loaded with the previous URL
      this.ensureIframeLoadedAfterRegeneration();
    } else {
      this.logger.info('❌ Regeneration failed - showing error in preview');

      // Show error state in preview
      this.showDeploymentErrorInPreview();
    }

    this.logger.info('✅ Regeneration failure handled');
  }

  /**
   * Complete direct regeneration response (no polling involved)
   * ENHANCED: Handles regeneration completion for direct API responses
   */
  private completeDirectRegenerationResponse(): void {
    this.logger.info('🎯 Completing direct regeneration response');

    // Calculate regeneration duration if it was started
    if (this.regenerationStartTime > 0) {
      const regenerationDuration = Date.now() - this.regenerationStartTime;
      this.logger.info(`⏱️ Regeneration completed in ${regenerationDuration}ms`);
    }

    // Clear regeneration state
    this.isRegenerationInProgress$.next(false);
    this.regenerationStartTime = 0;

    // Stop preview loading state
    this.stopRegenerationPreviewLoading();

    // Enable preview tab and clear errors
    this.isPreviewTabEnabled = true;
    this.previewError$.next(false);

    // Get the current deployed URL to show in iframe
    const currentUrl = this.deployedUrl$.value;

    if (currentUrl && currentUrl !== 'ERROR_DEPLOYMENT_FAILED') {
      this.logger.info('🔄 Regeneration completed successfully - loading iframe with deployed URL');
      // Ensure iframe is loaded with the current deployed URL
      this.ensureIframeLoadedAfterRegeneration();
    } else {
      this.logger.warn('⚠️ No deployed URL available after regeneration - keeping current state');
      // If no deployed URL, just clear the loading state and keep the current iframe state
      this.isPreviewLoading$.next(false);
      this.previewIcon$.next('bi-eye');

      // Switch to preview tab anyway to show the current state
      if (this.currentView$.value !== 'preview') {
        this.currentView$.next('preview');
      }
    }

    // Force change detection
    this.cdr.detectChanges();

    this.logger.info('✅ Direct regeneration response completed successfully');
  }

  /**
   * Ensure iframe is properly loaded after regeneration (success or failure)
   * ENHANCED: Manages iframe validation states and loading for regeneration scenarios
   */
  private ensureIframeLoadedAfterRegeneration(): void {
    const currentUrl = this.deployedUrl$.value;

    if (!currentUrl || currentUrl === 'ERROR_DEPLOYMENT_FAILED') {
      this.logger.warn('⚠️ No valid URL available for iframe loading after regeneration');
      return;
    }

    this.logger.info('🔄 Ensuring iframe is loaded after regeneration with URL:', currentUrl);

    // Reset iframe validation states to ensure proper loading
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);
    this.isIframeReady$.next(false);

    // Clear any validation errors
    this.urlValidationError$.next('');

    // Temporarily clear iframe to force reload
    this.urlSafe = undefined;
    this.cdr.detectChanges();

    // Re-process the URL with proper validation and iframe setup
    setTimeout(() => {
      // Validate and sanitize the URL
      if (this.isValidPreviewUrl(currentUrl)) {
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(currentUrl);

        // Set validation states to indicate iframe is ready
        this.isUrlValidated$.next(true);
        this.isUrlAvailable$.next(true);
        this.isIframeReady$.next(true);

        // Ensure preview tab is enabled and loading is stopped
        this.isPreviewTabEnabled = true;
        this.isPreviewLoading$.next(false);
        this.previewIcon$.next('bi-eye');
        this.previewError$.next(false);

        // Switch to preview tab to show the iframe
        if (this.currentView$.value !== 'preview') {
          this.currentView$.next('preview');
        }

        this.cdr.detectChanges();
        this.logger.info('✅ Iframe loaded successfully after regeneration');
      } else {
        this.logger.error('❌ Invalid URL for iframe loading after regeneration');
        this.showDeploymentErrorInPreview();
      }
    }, 100);
  }

  /**
   * Get current files from version management result
   * This method constructs the current file list based on version management decisions
   */
  private getCurrentFilesFromVersionManagement(versionResult: any): FileModel[] {
    const currentFiles: FileModel[] = [];

    // Iterate through all file versions to build current state
    for (const [filePath, fileHistory] of versionResult.fileVersions.entries()) {
      const currentVersion = fileHistory.versions.find((v: any) => v.metadata.isCurrentVersion);
      if (currentVersion) {
        const fileModel: FileModel = {
          name: filePath,
          type: 'file',
          content: currentVersion.content,
          fileName: filePath
        };
        currentFiles.push(fileModel);
      }
    }

    this.logger.info(`📂 Constructed ${currentFiles.length} files from version management`);
    return currentFiles;
  }

  /**
   * Create merged file list from current files and comparison result
   * This method combines current files with updates and additions from edit responses
   */
  private createMergedFileList(currentFiles: FileModel[], comparisonResult: any): FileModel[] {
    const mergedFiles: FileModel[] = [...currentFiles];

    // Apply updates to existing files
    for (const updatedFile of comparisonResult.updated) {
      const existingIndex = mergedFiles.findIndex(file => {
        const currentPath = this.normalizeFilePath(file.name || file.fileName || '');
        const updatedPath = this.normalizeFilePath(updatedFile.name || updatedFile.fileName || '');
        return currentPath === updatedPath;
      });

      if (existingIndex !== -1) {
        mergedFiles[existingIndex] = { ...mergedFiles[existingIndex], ...updatedFile };
        this.logger.info(`🔄 Applied update to: ${updatedFile.name || updatedFile.fileName}`);
      }
    }

    // Add new files
    for (const newFile of comparisonResult.added) {
      mergedFiles.push(newFile);
      this.logger.info(`➕ Added new file: ${newFile.name || newFile.fileName}`);
    }

    this.logger.info(`✅ Created merged file list: ${mergedFiles.length} total files`);
    return mergedFiles;
  }

  /**
   * Get current code files from Monaco editor
   * ENHANCED: Multiple sources with flattening and comprehensive logging
   */
  private getCurrentCodeFiles(): FileModel[] {
    try {
      this.logger.info('🔍 Getting current code files from multiple sources');

      let currentFiles: FileModel[] = [];

      // Source 1: Try to get files directly from code-viewer component (flattened)
      if (this.codeViewer && this.codeViewer.getAllCurrentFiles) {
        const codeViewerFiles = this.codeViewer.getAllCurrentFiles();
        if (codeViewerFiles && codeViewerFiles.length > 0) {
          currentFiles = codeViewerFiles;
          this.logger.info(`📂 Retrieved ${currentFiles.length} flattened files from code-viewer component`);
        }
      }
      // Source 1b: Fallback to code-viewer files property
      else if (this.codeViewer && this.codeViewer.files && this.codeViewer.files.length > 0) {
        currentFiles = this.codeViewer.files;
        this.logger.info(`📂 Retrieved ${currentFiles.length} files from code-viewer files property`);
      }
      // Source 2: Get files from the current state
      else if (this.files$.value && this.files$.value.length > 0) {
        currentFiles = this.files$.value;
        this.logger.info(`📂 Retrieved ${currentFiles.length} files from files$ observable`);
      }
      // Source 3: Fallback to generated code from code sharing service
      else {
        this.logger.info('📂 No files in primary sources, checking code sharing service');
        const generatedCode = this.codeSharingService.getGeneratedCode();
        if (generatedCode) {
          currentFiles = this.convertGeneratedCodeToFileModels(generatedCode);
          this.logger.info(`📂 Retrieved ${currentFiles.length} files from code sharing service`);
        } else {
          this.logger.warn('⚠️ No files found in any source');
          return [];
        }
      }

      // Flatten the file tree to get all files (including nested ones)
      const flattenedFiles = this.flattenFileTree(currentFiles);

      this.logger.info(`📂 Final flattened files: ${flattenedFiles.length} total files`);
      flattenedFiles.forEach((file, index) => {
        const filePath = file.name || file.fileName || 'unknown';
        const contentLength = (file.content || '').length;
        this.logger.info(`  ${index + 1}. "${filePath}" (${contentLength} chars)`);
      });

      return flattenedFiles;
    } catch (error) {
      this.logger.error('Error getting current code files:', error);
      return [];
    }
  }

  /**
   * Flatten file tree to get all files including nested ones
   * This ensures we can properly compare against files in folders
   */
  private flattenFileTree(files: FileModel[]): FileModel[] {
    const flatFiles: FileModel[] = [];

    const processFile = (file: FileModel, parentPath: string = '') => {
      if (file.type === 'file') {
        // For files, use the full path if available, otherwise construct it
        const fullPath = file.fileName || file.name || '';
        const finalPath = parentPath && !fullPath.startsWith(parentPath)
          ? `${parentPath}/${file.name}`
          : fullPath;

        flatFiles.push({
          ...file,
          name: finalPath,
          fileName: finalPath
        });
      } else if (file.type === 'folder' && file.children) {
        // For folders, process children with updated parent path
        const folderPath = parentPath ? `${parentPath}/${file.name}` : file.name;
        file.children.forEach(child => processFile(child, folderPath));
      }
    };

    files.forEach(file => processFile(file));
    return flatFiles;
  }

  /**
   * Convert generated code to FileModel format
   */
  private convertGeneratedCodeToFileModels(generatedCode: any): FileModel[] {
    const fileModels: FileModel[] = [];

    try {
      if (typeof generatedCode === 'string') {
        // Try to parse as JSON
        try {
          const parsedCode = JSON.parse(generatedCode);
          return this.convertGeneratedCodeToFileModels(parsedCode);
        } catch {
          // Single file content
          fileModels.push({
            name: 'index.html',
            type: 'file',
            content: generatedCode,
            fileName: 'index.html'
          });
        }
      } else if (Array.isArray(generatedCode)) {
        // Array of files
        for (const item of generatedCode) {
          if (typeof item === 'object' && item !== null) {
            const fileName = item.fileName || item.name || item.path || 'unknown.txt';
            const content = item.content || '';
            fileModels.push({
              name: fileName,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              fileName: fileName
            });
          }
        }
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {
        // Object with file paths as keys
        for (const [filePath, content] of Object.entries(generatedCode)) {
          fileModels.push({
            name: filePath,
            type: 'file',
            content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            fileName: filePath
          });
        }
      }
    } catch (error) {
      this.logger.error('Error converting generated code to file models:', error);
    }

    return fileModels;
  }

  /**
   * Handle successful edit response
   * ENHANCED: Handles both string and direct array response formats
   */
  private handleEditResponse(response: any): void {
    try {
      this.logger.info('🔄 Processing edit response:', response);

      // Update the last AI message
      const lastMessage = this.lightMessages[this.lightMessages.length - 1];
      if (lastMessage && lastMessage.from === 'ai') {
        lastMessage.text = 'Edit completed successfully! Updating code...';
      }

      // Check if response contains direct file data
      if (typeof response === 'string') {
        // Original format: string of array of JSON
        this.processDirectEditResponse(response);
      } else if (Array.isArray(response)) {
        // New format: direct array of JSON objects
        this.logger.info('📦 Processing direct array response format');
        this.processDirectEditResponse(JSON.stringify(response));
      } else if (response.project_id && response.job_id) {
        // Fallback: Start polling for the edit results
        // 🛡️ CRITICAL: Block polling service in UI Design mode
        if (!this.isUIDesignMode$.value) {
          this.projectId = response.project_id;
          this.jobId = response.job_id;

          // Start polling service
          this.pollingService.startPolling(response.project_id, response.job_id, {
            taskType: 'code-edit'
          });
        } else {
          this.logger.info('🚫 Blocking edit polling service in UI Design mode - maintaining isolation');
        }

        this.isPolling = true;

        if (lastMessage && lastMessage.from === 'ai') {
          lastMessage.text = 'Edit request submitted successfully! Starting code regeneration...';
        }
      } else {
        this.logger.error('❌ Unrecognized response format:', typeof response, response);
        this.handleEditError('Invalid response from edit service. Please try again.');
      }
    } catch (error) {
      this.logger.error('Error handling edit response:', error);
      this.handleEditError('Failed to process edit response. Please try again.');
    } finally {
      this.isAiResponding = false;
    }
  }

  /**
   * Process direct edit response (string of array of JSON with file paths as keys OR direct array of JSON)
   * ENHANCED: Robust file tree persistence and comparison system
   */
  private processDirectEditResponse(responseString: string): void {
    try {
      this.logger.info('📝 Processing direct edit response with robust file tree comparison');

      let editedFiles: any;

      // Handle both response formats:
      // 1. String containing array of JSON objects (original format)
      // 2. Direct array of JSON objects (new format)
      try {
        // First, try to parse as JSON (original format)
        editedFiles = JSON.parse(responseString);
        this.logger.info('✅ Successfully parsed response as JSON');
      } catch (parseError) {
        this.logger.error('❌ Failed to parse response as JSON:', parseError);
        this.handleEditError('Invalid response format. Please try again.');
        return;
      }

      // Validate that we have an array
      if (!Array.isArray(editedFiles)) {
        this.logger.error('❌ Edit response is not an array:', editedFiles);
        this.handleEditError('Invalid edit response format. Please try again.');
        return;
      }

      this.logger.info(`✏️ Processing ${editedFiles.length} edited files`);

      // Extract edited files from the response
      const editedFileData = this.extractEditedFilesFromResponse(editedFiles);

      if (editedFileData.length === 0) {
        this.logger.warn('⚠️ No valid files found in edit response');
        this.handleEditError('No files found in edit response. Please try again.');
        return;
      }

      // ENHANCED: Use version management system for AI regeneration
      // Check if user has made edits and handle accordingly
      const versionResult = this.fileTreePersistenceService.processAiRegeneration(
        editedFileData,
        true // preserveUserEdits = true by default
      );

      this.logger.info(`📊 Version management result: ${versionResult.preservedUserEdits.length} preserved, ${versionResult.overwrittenFiles.length} overwritten, ${versionResult.conflictingFiles.length} conflicts`);

      // Get current files based on version management decisions
      const currentFiles = this.getCurrentFilesFromVersionManagement(versionResult);
      this.logger.info(`📂 Current files from version management: ${currentFiles.length}`);

      // Clear dirty states for overwritten files only
      for (const overwrittenFile of versionResult.overwrittenFiles) {
        this.monacoStateManagementService.markFileAsSaved(overwrittenFile);
      }

      // Update persistence service with new current state
      this.fileTreePersistenceService.updateCurrentState(currentFiles);

      // Update the files observable with current files
      this.files$.next(currentFiles);

      // Update code sharing service
      this.codeSharingService.setGeneratedCode(currentFiles);

      // Update code viewer with the current files
      if (this.codeViewer) {
        // Set the complete current files to ensure synchronization
        this.codeViewer.files = currentFiles;
        this.logger.info('🔄 Updated code-viewer with complete file tree');

        // Also use batch update for individual file updates if available
        if (this.codeViewer.updateMultipleFiles) {
          this.codeViewer.updateMultipleFiles(editedFileData);
          this.logger.info('🔄 Applied batch updates to code-viewer');
        }
      }

      // Update the last AI message with detailed results
      const lastMessage = this.lightMessages[this.lightMessages.length - 1];
      if (lastMessage && lastMessage.from === 'ai') {
        lastMessage.text = `✅ Edit completed! Preserved ${versionResult.preservedUserEdits.length} user edits, updated ${versionResult.overwrittenFiles.length} files, ${versionResult.conflictingFiles.length} conflicts detected.`;
      }

      // Promote current state to new baseline for future comparisons
      this.fileTreePersistenceService.promoteToBaseline();
      this.logger.info('⬆️ Promoted current state to new baseline for future comparisons');

      // ENHANCED: For direct edit responses, complete regeneration immediately
      // since there's no polling involved - the response contains the final result
      this.logger.info('🎯 Direct edit response processed successfully - completing regeneration');
      this.completeDirectRegenerationResponse();

      this.logger.info('✅ Direct edit response processed successfully with robust file tree persistence');

    } catch (error) {
      this.logger.error('❌ Error processing direct edit response:', error);
      this.handleEditError('Failed to process edit response. Please try again.');
      // ENHANCED: Stop preview loading on error
      this.stopRegenerationPreviewLoading();
    }
  }

  /**
   * Extract edited files from response array
   * Handles both formats: array of objects with fileName/content OR array of objects with file paths as keys
   * ENHANCED: Better validation and logging for your specific JSON format
   */
  private extractEditedFilesFromResponse(editedFiles: any[]): { fileName: string; content: string }[] {
    const extractedFiles: { fileName: string; content: string }[] = [];

    this.logger.info(`🔍 Processing ${editedFiles.length} files from edit response`);

    for (const fileData of editedFiles) {
      if (typeof fileData === 'object' && fileData !== null) {
        // Format 1: Object with fileName and content properties (your format)
        if (fileData.fileName && fileData.content !== undefined) {
          const fileName = fileData.fileName.trim();
          const content = typeof fileData.content === 'string' ? fileData.content : JSON.stringify(fileData.content, null, 2);

          extractedFiles.push({
            fileName: fileName,
            content: content
          });
          this.logger.info(`📄 Extracted file (fileName/content format): ${fileName} (${content.length} chars)`);
        }
        // Format 2: Object where keys are file paths and values are content
        else {
          for (const [filePath, content] of Object.entries(fileData)) {
            if (typeof filePath === 'string' && content !== undefined) {
              const cleanPath = filePath.trim();
              const fileContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);

              extractedFiles.push({
                fileName: cleanPath,
                content: fileContent
              });
              this.logger.info(`📄 Extracted file (key/value format): ${cleanPath} (${fileContent.length} chars)`);
            }
          }
        }
      } else {
        this.logger.warn(`⚠️ Skipping invalid file data:`, fileData);
      }
    }

    this.logger.info(`✅ Successfully extracted ${extractedFiles.length} files for processing`);
    return extractedFiles;
  }

  /**
   * Smart merge files: Update existing files and add new ones while maintaining file tree structure
   * ENHANCED: Comprehensive logging and precise file matching
   */
  private smartMergeFiles(currentFiles: FileModel[], editedFiles: { fileName: string; content: string }[]): FileModel[] {
    const mergedFiles: FileModel[] = [...currentFiles];
    const processedPaths = new Set<string>();

    this.logger.info('🔄 Starting smart file merge');
    this.logger.info(`📊 Input: ${currentFiles.length} current files, ${editedFiles.length} edited files`);

    // Log all current files for debugging
    this.logger.info('📂 Current files in Monaco editor:');
    currentFiles.forEach((file, index) => {
      const normalizedPath = this.normalizeFilePath(file.name || file.fileName || '');
      this.logger.info(`  ${index + 1}. "${file.name || file.fileName}" → normalized: "${normalizedPath}"`);
    });

    // Log all edited files for debugging
    this.logger.info('✏️ Edited files to process:');
    editedFiles.forEach((file, index) => {
      const normalizedPath = this.normalizeFilePath(file.fileName);
      this.logger.info(`  ${index + 1}. "${file.fileName}" → normalized: "${normalizedPath}"`);
    });

    for (const editedFile of editedFiles) {
      const normalizedEditedPath = this.normalizeFilePath(editedFile.fileName);

      // Skip if we've already processed this path
      if (processedPaths.has(normalizedEditedPath)) {
        this.logger.warn(`⚠️ Duplicate file path detected, skipping: ${normalizedEditedPath}`);
        continue;
      }
      processedPaths.add(normalizedEditedPath);

      this.logger.info(`🔍 Processing edited file: "${editedFile.fileName}" (normalized: "${normalizedEditedPath}")`);

      // Find existing file by comparing normalized paths
      const existingFileIndex = mergedFiles.findIndex((file, index) => {
        const currentPath = this.normalizeFilePath(file.name || file.fileName || '');
        const match = currentPath === normalizedEditedPath;

        this.logger.info(`  Comparing with file ${index + 1}: "${file.name || file.fileName}" (normalized: "${currentPath}") → match: ${match}`);

        if (match) {
          this.logger.info(`🎯 MATCH FOUND! File "${editedFile.fileName}" exists at index ${index}`);
        }

        return match;
      });

      if (existingFileIndex !== -1) {
        // Update existing file content
        const existingFile = mergedFiles[existingFileIndex];
        const oldContent = existingFile.content || '';

        mergedFiles[existingFileIndex] = {
          ...existingFile,
          content: editedFile.content,
          fileName: editedFile.fileName // Ensure fileName is set correctly
        };

        this.logger.info(`🔄 UPDATED existing file: "${editedFile.fileName}" at index ${existingFileIndex}`);
        this.logger.info(`   Content: ${oldContent.length} → ${editedFile.content.length} chars`);
      } else {
        // Add new file
        const newFile: FileModel = {
          name: editedFile.fileName,
          type: 'file',
          content: editedFile.content,
          fileName: editedFile.fileName
        };
        mergedFiles.push(newFile);
        this.logger.info(`➕ ADDED new file: "${editedFile.fileName}" (${editedFile.content.length} chars)`);
      }
    }

    this.logger.info(`✅ Smart merge completed: ${currentFiles.length} → ${mergedFiles.length} total files`);

    // Log final result
    this.logger.info('📋 Final merged files:');
    mergedFiles.forEach((file, index) => {
      this.logger.info(`  ${index + 1}. "${file.name || file.fileName}" (${(file.content || '').length} chars)`);
    });

    return mergedFiles;
  }

  /**
   * Normalize file path for comparison (handle different path separators and formats)
   */
  private normalizeFilePath(filePath: string): string {
    if (!filePath) return '';

    // Convert backslashes to forward slashes and remove leading/trailing slashes
    return filePath
      .replace(/\\/g, '/')
      .replace(/^\/+/, '')
      .replace(/\/+$/, '')
      .toLowerCase();
  }



  /**
   * Handle edit errors
   * ENHANCED: Properly clear regeneration state on errors
   */
  private handleEditError(errorMessage: string): void {
    this.logger.error('🚨 Handling edit error:', errorMessage);

    // Clear regeneration state if it was in progress
    if (this.isRegenerationInProgress$.value) {
      this.logger.info('🧹 Clearing regeneration state due to error');
      this.isRegenerationInProgress$.next(false);
      this.regenerationStartTime = 0;
      this.stopRegenerationPreviewLoading();
    }

    // Update the last AI message with error
    const lastMessage = this.lightMessages[this.lightMessages.length - 1];
    if (lastMessage && lastMessage.from === 'ai') {
      lastMessage.text = errorMessage;
    } else {
      // Add new error message if no AI message exists
      this.lightMessages.push({
        text: errorMessage,
        from: 'ai',
        theme: 'light',
      });
    }

    // Clear AI responding state
    this.isAiResponding = false;

    // Force change detection
    this.cdr.detectChanges();

    this.logger.info('✅ Edit error handled and states cleared');
  }

  handleEnhancedAlternate(): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';

    fileInput.addEventListener('change', (event: Event) => {
      const input = event.target as HTMLInputElement;
      if (input.files?.length) {
        alert('File uploaded: ' + input.files[0].name);
      }
    });

    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  }

  handleEnhanceText(): void {
    if (!this.lightPrompt.trim()) {
      return;
    }

    this.rightIcons[1].status = 'disable'; // Disable the enhance button while enhancing
    const originalPrompt = this.lightPrompt;

    // Add a message to show that enhancement is in progress
    this.lightMessages.push({
      text: 'Enhancing your prompt to provide more detailed instructions...',
      from: 'ai',
      theme: 'light',
    });

    // Call the prompt service to enhance the prompt
    this.promptService.enhancePrompt(this.lightPrompt, 'Generate Application').subscribe({
      next: (response: any) => {
        if (response && response.code && response.status_code === 200) {
          // Update the prompt with the enhanced version
          this.lightPrompt = response.code;

          // Add a message to show the enhanced prompt
          this.lightMessages.push({
            text:
              "I've enhanced your prompt to provide more detailed instructions. Here's the enhanced version:\n\n" +
              response.code,
            from: 'ai',
            theme: 'light',
          });

          this.logger.debug('Original prompt:', originalPrompt);
          this.logger.debug('Enhanced Prompt', this.lightPrompt);
        } else {
          // Add a message to show that enhancement failed
          this.lightMessages.push({
            text: "I couldn't enhance your prompt. Please try again with more details.",
            from: 'ai',
            theme: 'light',
          });
          // this.logger.error('Could not enhance prompt. Please try again.');
        }
      },
      error: (error: any) => {
        // this.logger.error('Error enhancing prompt:', error);
        // Add a message to show that enhancement failed
        this.lightMessages.push({
          text: 'I encountered an error while enhancing your prompt. Please try again.',
          from: 'ai',
          theme: 'light',
        });
      },
      complete: () => {
        this.rightIcons[1].status = 'active'; // Re-enable the enhance button
      },
    });
  }

  onIconClick(_iconName: string): void {
    // Your existing logic
    // Unused parameter prefixed with underscore
  }

  // isTryToFixButtonVisible(): boolean {
  //   return !this.tryToFixClicked && this.status === 'FAILED';
  // }


  /**
   * Toggle the export modal
   */
  toggleExportModal(): void {
    // this.logger.info('toggleExportModal called', {
    //   isCodeGenerationComplete: this.isCodeGenerationComplete,
    //   previewError: this.previewError,
    //   isExperienceStudioModalOpen: this.isExperienceStudioModalOpen
    // });

    // No validation - allow opening the modal regardless of code generation status
    this.isExperienceStudioModalOpen$.next(!this.isExperienceStudioModalOpen$.value);

    // If we're opening the modal, log it
    if (this.isExperienceStudioModalOpen$.value) {
      // this.logger.info('Export modal opened');
      // this.logger.info('Export modal opened');
    } else {
      // this.logger.info('Export modal closed');
      // this.logger.info('Export modal closed');
    }

    // View will update automatically via reactive patterns
  }

  /**
   * Copy text to clipboard
   * @param inputElement The input element containing the text to copy
   */
  copyToClipboard(inputElement: HTMLInputElement): void {
    // this.logger.info('Copying link to clipboard');

    try {
      inputElement.select();
      document.execCommand('copy');
      inputElement.setSelectionRange(0, 0); // Deselect

      // Show toast notification
      this.toastService.success('Link copied to clipboard');
      // this.logger.info('Link copied to clipboard');
    } catch (error) {
      this.logger.error('Failed to copy link to clipboard', error);
      this.toastService.error('Failed to copy link to clipboard');
    }
  }

  /**
   * Export to VSCode
   * ENHANCED: Real implementation using VSCode export service
   */
  exportToVSCode(): void {
    this.logger.info('🚀 Starting VSCode export process');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show initial toast notification
    this.toastService.info('Preparing VSCode export...');

    try {
      // Get the generated code from multiple sources
      const generatedCode = this.getCodeFromMultipleSources();

      if (!generatedCode) {
        this.toastService.error('No code available to export. Please ensure code generation is complete.');
        this.logger.warn('❌ No code available for VSCode export');
        return;
      }

      // Generate app name for the export
      const appName = this.generateAppNameForDownload();
      this.logger.info('📦 Preparing VSCode export for app:', appName);

      // Prepare files for export
      const exportFiles = this.prepareFilesForVSCodeExport(generatedCode);

      // Use the VSCode export service
      this.vscodeExportService.exportToVSCode({
        projectName: appName,
        files: exportFiles,
        openInVSCode: true,
        downloadFallback: true
      }).subscribe({
        next: (result) => {
          this.handleVSCodeExportResult(result, appName);
        },
        error: (error) => {
          this.logger.error('💥 VSCode export failed:', error);
          this.toastService.error('Failed to export to VSCode. Please try downloading the project instead.');
        }
      });

    } catch (error) {
      this.logger.error('💥 Error during VSCode export preparation:', error);
      this.toastService.error('Failed to prepare VSCode export. Please try again.');
    }
  }

  /**
   * Prepare files for VSCode export
   * ENHANCED: Convert generated code to format suitable for VSCode export
   */
  private prepareFilesForVSCodeExport(generatedCode: any): any[] {
    this.logger.info('🔧 Preparing files for VSCode export');

    const exportFiles: any[] = [];

    try {
      if (typeof generatedCode === 'string') {
        // Handle string content - try to parse as JSON first
        try {
          const parsedCode = JSON.parse(generatedCode);
          this.processCodeForVSCodeExport(parsedCode, exportFiles);
        } catch {
          // If not JSON, treat as single file
          exportFiles.push({
            name: 'index.html',
            content: generatedCode,
            path: 'index.html'
          });
        }
      } else if (Array.isArray(generatedCode)) {
        // Handle array of files
        for (const item of generatedCode) {
          if (typeof item === 'object' && item !== null) {
            const fileName = item.fileName || item.name || item.path || 'unknown.txt';
            const content = item.content || '';
            exportFiles.push({
              name: fileName,
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              path: fileName
            });
          }
        }
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {
        // Handle object with file paths as keys
        this.processCodeForVSCodeExport(generatedCode, exportFiles);
      }

      this.logger.info('✅ Prepared files for VSCode export:', exportFiles.length, 'files');
      return exportFiles;

    } catch (error) {
      this.logger.error('💥 Error preparing files for VSCode export:', error);
      return [];
    }
  }

  /**
   * Process code object for VSCode export
   */
  private processCodeForVSCodeExport(codeObject: any, exportFiles: any[]): void {
    for (const [filePath, content] of Object.entries(codeObject)) {
      exportFiles.push({
        name: filePath,
        content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
        path: filePath
      });
    }
  }

  /**
   * Handle VSCode export result
   * ENHANCED: Process different export result types and show appropriate feedback
   */
  private handleVSCodeExportResult(result: VSCodeExportResult, appName: string): void {
    this.logger.info('📋 Handling VSCode export result:', result);

    switch (result.method) {
      case 'vscode-protocol':
        if (result.success) {
          this.toastService.success('🎉 VSCode opened with your project!');
          this.toastService.info('Your project files have been created and VSCode should be opening now.');
          this.logger.info('✅ VSCode protocol method succeeded');
        } else {
          this.toastService.warning('VSCode protocol attempted but may not have worked.');
          this.toastService.info('If VSCode didn\'t open, please check your VSCode installation.');
        }
        break;

      case 'download-fallback':
        const downloadFileName = result.downloadFileName || `${appName.toLowerCase()}.zip`;
        this.toastService.success(`📦 Project downloaded as "${downloadFileName}"`);
        this.toastService.info(`💡 Extract ${downloadFileName} → Open VSCode → File → Open Folder → Select extracted folder`);
        this.toastService.info('🔧 For best experience: Open the .code-workspace file included in the download');
        this.logger.info('✅ Download fallback method succeeded', { downloadFileName });
        break;

      case 'manual-instructions':
        this.toastService.warning('⚠️ Please copy the files manually to your VSCode project');
        this.showManualVSCodeInstructions(appName);
        this.logger.info('⚠️ Manual instructions method used');
        break;

      default:
        this.toastService.error('❌ Unknown export method. Please try downloading the project instead.');
        this.logger.error('❌ Unknown export method:', result.method);
        break;
    }

    // Show additional helpful information
    if (result.success && result.method === 'vscode-protocol') {
      setTimeout(() => {
        this.toastService.info('💡 Tip: Look for the new project folder and open the .code-workspace file in VSCode for the best experience!');
      }, 3000);
    }

    // Log the export completion
    this.logger.info('🎯 VSCode export process completed', {
      method: result.method,
      success: result.success,
      appName
    });
  }

  /**
   * Show manual VSCode instructions
   */
  private showManualVSCodeInstructions(appName: string): void {
    const instructions = `
To manually import your project into VSCode:

1. Copy all the generated files to a local folder
2. Open VSCode
3. Go to File → Open Folder
4. Select your project folder
5. Install recommended extensions when prompted

Your project "${appName}" is ready for development!
    `;

    this.logger.info('📋 Manual VSCode instructions:', instructions);

    // You could show this in a modal or extended toast if needed
    // For now, we'll just log it and show a toast
    this.toastService.info('Check the console for detailed VSCode import instructions');
  }

  /**
   * Export to Azure
   */
  exportToAzure(): void {
    this.logger.info('Exporting to Azure');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Preparing Azure export...');

    // In a real implementation, this would trigger the Azure export process
    setTimeout(() => {
      this.toastService.success('Project exported to Azure');
      this.logger.info('Project exported to Azure');
    }, 1500);
  }

  /**
   * Download project as a zip file with proper tree structure
   * ENHANCED: Improved error handling and multiple data source support
   */
  async downloadProject(): Promise<void> {
    this.logger.info('🔽 Starting project download process');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Preparing download...');

    try {
      // Get the generated code from multiple sources
      let generatedCode = this.getCodeFromMultipleSources();

      if (!generatedCode) {
        this.toastService.error('No code available to download. Please ensure code generation is complete.');
        this.logger.warn('❌ No code available for download');
        return;
      }

      // Generate app name for the zip file
      const appName = this.generateAppNameForDownload();
      this.logger.info('📦 Creating zip file for app:', appName);

      // Create a new JSZip instance
      const zip = new JSZip();

      // Create the main project folder
      const projectFolder = zip.folder(appName);

      if (!projectFolder) {
        throw new Error('Failed to create project folder in zip');
      }

      // Process the generated code and add files to zip
      this.toastService.info('Processing files...');
      await this.addFilesToZip(projectFolder, generatedCode);

      // Add additional project files
      this.addProjectMetadata(projectFolder, appName);

      // Generate the zip file
      this.toastService.info('Creating zip file...');
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      });

      // Create download link and trigger download
      this.triggerDownload(zipBlob, appName);

      // Show success notification
      this.toastService.success(`Project "${appName}" downloaded successfully`);
      this.logger.info('✅ Project zip file downloaded successfully', {
        appName,
        size: `${(zipBlob.size / 1024 / 1024).toFixed(2)} MB`
      });

    } catch (error) {
      this.logger.error('💥 Error downloading project:', error);
      this.toastService.error('Error creating project download. Please try again.');

      // Log additional context for debugging
      if (error instanceof Error) {
        this.logger.error('Error details:', error.message);
      }
    }
  }

  /**
   * Get code from multiple sources with fallback
   * ENHANCED: Tries multiple sources to get the generated code
   */
  private getCodeFromMultipleSources(): any {
    this.logger.info('🔍 Searching for generated code from multiple sources');

    // Source 1: Code sharing service
    let generatedCode = this.codeSharingService.getGeneratedCode();
    if (generatedCode) {
      this.logger.info('✅ Found code from code sharing service');
      return generatedCode;
    }

    // Source 2: Files observable (get current value)
    let currentFiles: any = null;
    this.files.subscribe(files => currentFiles = files).unsubscribe();
    if (currentFiles && currentFiles.length > 0) {
      this.logger.info('✅ Found code from files observable:', currentFiles.length, 'files');
      return currentFiles;
    }

    // Source 3: Artifacts data (if available)
    if (this.artifactsData && this.artifactsData.length > 0) {
      const codeArtifacts = this.artifactsData.filter(artifact =>
        artifact.type === 'file' || artifact.type === 'code'
      );
      if (codeArtifacts.length > 0) {
        this.logger.info('✅ Found code from artifacts data:', codeArtifacts.length, 'artifacts');
        return codeArtifacts;
      }
    }

    this.logger.warn('❌ No code found from any source');
    return null;
  }

  /**
   * Trigger the actual download
   * ENHANCED: Separated download logic for better error handling
   */
  private triggerDownload(zipBlob: Blob, appName: string): void {
    try {
      // Create download link
      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${appName}.zip`;
      link.style.display = 'none';

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Clean up immediately
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.logger.info('🎯 Download triggered successfully');
    } catch (error) {
      this.logger.error('💥 Error triggering download:', error);
      throw new Error('Failed to trigger download');
    }
  }

  /**
   * Generate app name for download
   */
  private generateAppNameForDownload(): string {
    // Try to get app name from various sources
    if (this.appName) {
      return this.appName;
    }

    if (this.projectName) {
      return this.projectName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    // Get from prompt service or generate based on current data
    const promptData = this.promptService.getCurrentPromptData();
    if (promptData?.selectedCardTitle) {
      const baseName = promptData.selectedCardTitle.toLowerCase().replace(/[^a-z0-9]/g, '-');
      return `${baseName}-app`;
    }

    // Fallback to timestamp-based name
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `generated-app-${timestamp}`;
  }

  /**
   * Add files to zip with proper tree structure
   */
  private async addFilesToZip(projectFolder: JSZip, generatedCode: any): Promise<void> {
    if (typeof generatedCode === 'string') {
      // Handle string content - try to parse as JSON first
      try {
        const parsedCode = JSON.parse(generatedCode);
        await this.processCodeObject(projectFolder, parsedCode);
      } catch {
        // If not JSON, treat as single file
        projectFolder.file('index.html', generatedCode);
      }
    } else if (Array.isArray(generatedCode)) {
      // Handle array of files
      for (const item of generatedCode) {
        if (typeof item === 'object' && item !== null) {
          const fileName = item.fileName || item.name || item.path || 'unknown.txt';
          const content = item.content || '';
          this.addFileToZipWithPath(projectFolder, fileName, content);
        }
      }
    } else if (typeof generatedCode === 'object' && generatedCode !== null) {
      // Handle object with file paths as keys
      await this.processCodeObject(projectFolder, generatedCode);
    }
  }

  /**
   * Process code object and add files with proper paths
   */
  private async processCodeObject(projectFolder: JSZip, codeObject: any): Promise<void> {
    for (const [filePath, content] of Object.entries(codeObject)) {
      const fileContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
      this.addFileToZipWithPath(projectFolder, filePath, fileContent);
    }
  }

  /**
   * Add file to zip with proper directory structure
   */
  private addFileToZipWithPath(projectFolder: JSZip, filePath: string, content: string): void {
    // Clean the file path
    const cleanPath = filePath.replace(/^\/+/, ''); // Remove leading slashes

    // Split path into directories and filename
    const pathParts = cleanPath.split('/');
    const fileName = pathParts.pop() || 'unknown.txt';

    // Create nested folders if needed
    let currentFolder = projectFolder;
    for (const folderName of pathParts) {
      if (folderName.trim()) {
        const existingFolder = currentFolder.folder(folderName);
        currentFolder = existingFolder || currentFolder.folder(folderName)!;
      }
    }

    // Add the file to the appropriate folder
    currentFolder.file(fileName, content);
  }

  /**
   * Add project metadata files
   */
  private addProjectMetadata(projectFolder: JSZip, appName: string): void {
    // Create README.md
    const readmeContent = this.generateReadmeContent(appName);
    projectFolder.file('README.md', readmeContent);

    //commented out for now-> if need in future we can use to add the package and gitignore to the download zip folder.

    // // Create package.json if it doesn't exist
    // const hasPackageJson = this.checkIfFileExists(projectFolder, 'package.json');
    // if (!hasPackageJson) {
    //   const packageJsonContent = this.generatePackageJsonContent(appName);
    //   projectFolder.file('package.json', packageJsonContent);
    // }

    // Create .gitignore if it doesn't exist
    // const hasGitignore = this.checkIfFileExists(projectFolder, '.gitignore');
    // if (!hasGitignore) {
    //   const gitignoreContent = this.generateGitignoreContent();
    //   projectFolder.file('.gitignore', gitignoreContent);
    // }
  }

  /**
   * Check if a file exists in the zip folder
   */
  private checkIfFileExists(folder: JSZip, fileName: string): boolean {
    return folder.file(fileName) !== null;
  }

  /**
   * Generate README.md content
   */
  private generateReadmeContent(appName: string): string {
    const currentDate = new Date().toLocaleDateString();
    return `# ${appName}

Generated on: ${currentDate}

## Description
This project was generated using the Experience Studio platform.

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn

### Installation
\`\`\`bash
npm install
\`\`\`

### Running the Application
\`\`\`bash
npm start
\`\`\`

### Building for Production
\`\`\`bash
npm run build
\`\`\`

## Project Structure
- \`src/\` - Source code files
- \`public/\` - Static assets

## Support
For support and questions, please refer to the Experience Studio documentation.
`;
  }

  /**
   * Generate package.json content
   */
  // private generatePackageJsonContent(appName: string): string {
  //   const packageJson = {
  //     name: appName,
  //     version: "1.0.0",
  //     description: "Generated by Experience Studio",
  //     main: "index.html",
  //     scripts: {
  //       start: "npx serve .",
  //       build: "echo 'Build completed'",
  //       dev: "npx serve . -l 3000"
  //     },
  //     keywords: ["experience-studio", "generated", "web-app"],
  //     author: "Experience Studio",
  //     license: "MIT",
  //     devDependencies: {
  //       serve: "^14.0.0"
  //     }
  //   };

  //   return JSON.stringify(packageJson, null, 2);
  // }

  /**
   * Generate .gitignore content
   */
//   private generateGitignoreContent(): string {
//     return `# Dependencies
// node_modules/
// npm-debug.log*
// yarn-debug.log*
// yarn-error.log*

// # Production builds
// /build
// /dist

// # Environment variables
// .env
// .env.local
// .env.development.local
// .env.test.local
// .env.production.local

// # IDE files
// .vscode/
// .idea/
// *.swp
// *.swo

// # OS generated files
// .DS_Store
// .DS_Store?
// ._*
// .Spotlight-V100
// .Trashes
// ehthumbs.db
// Thumbs.db

// # Logs
// logs
// *.log

// # Runtime data
// pids
// *.pid
// *.seed
// *.pid.lock

// # Coverage directory used by tools like istanbul
// coverage/

// # Temporary folders
// tmp/
// temp/
// `;
//   }

  /**
   * Generate unit tests
   */
  generateUnitTests(): void {
    this.logger.info('Generating unit tests');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Generating unit tests...');

    // In a real implementation, this would trigger the unit test generation process
    setTimeout(() => {
      this.toastService.success('Unit tests generated successfully');
      this.logger.info('Unit tests generated successfully');
    }, 2000);
  }

  /**
   * Generate API
   */
  generateAPI(): void {
    // this.logger.info('Generating API');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Generating API...');

    // In a real implementation, this would trigger the API generation process
    setTimeout(() => {
      this.toastService.success('API generated successfully');
      // this.logger.info('API generated successfully');
    }, 2000);
  }

  /**
   * Handle tab click events
   * @param tab The name of the tab that was clicked
   */
  onTabClick(tab: string): void {
    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Handle tab clicks based on the tab name
    switch (tab) {
      case 'overview':
        this.toggleOverviewView();
        break;
      case 'preview':
        this.togglePreviewView();
        break;
      case 'code':
        // Only toggle code view if code is generated and there's no error
        if (this.isCodeGenerationComplete && !this.previewError$.value) {
          this.toggleCodeView();
        }
        break;
      case 'logs':
        // Only toggle logs view if logs are available
        if (this.hasLogs) {
          this.toggleLogsView();
        }
        break;
      case 'artifacts':
        // Only toggle artifacts view if artifacts tab is enabled
        if (this.isArtifactsTabEnabled) {
          this.toggleArtifactsView();
        }
        break;
    }
  }

  @HostListener('window:message', ['$event'])
  onMessage(event: MessageEvent) {
    if (event.data && event.data.type === 'elementSelected') {
      this.selectedElement = event.data.element;
      this.showEditorIcon = true;
    }
  }

  /**
   * Handles iframe load event
   * @param _event The load event (unused but kept for future implementation)
   */
  onIframeLoad(_event: Event): void {
    this.logger.info('Iframe loaded successfully');
    this.isPreviewLoading$.next(false);

    // If we're in element selection mode, inject the selection scripts
    if (this.isElementSelectionMode) {
      this.injectSelectionScripts();
    }
  }

  /**
   * Handle iframe error event
   */
  onIframeError(event: any): void {
    this.logger.error('Iframe failed to load:', event);
    this.previewError$.next(true);
    this.errorDescription$.next('Failed to load preview. The deployed application may not be accessible.');
  }

  /**
   * Toggles element selection mode on/off
   */
  toggleElementSelectionMode(): void {
    this.isElementSelectionMode = !this.isElementSelectionMode;

    // If we're turning on selection mode, make sure we're in preview view
    if (this.isElementSelectionMode) {
      if (this.currentView$.value !== 'preview') {
        this.togglePreviewView();
      }

      // Inject selection scripts into the iframe
      this.injectSelectionScripts();

      // Add a message to the chat window
      // this.lightMessages.push({
      //   text: "I've enabled element selection mode. Hover over any element in the preview and click to select it for editing.",
      //   from: 'ai',
      //   theme: 'light'
      // });
    } else {
      // Clean up selection mode
      this.cleanupSelectionMode();

      // If an element was selected, clear it
      if (this.selectedElement) {
        this.clearSelectedElement();
      }
    }

    // Force change detection
    this.cdr.detectChanges();
  }

  /**
   * Injects scripts into the iframe to enable element selection
   */
  private injectSelectionScripts(): void {
    try {
      // Get the iframe element
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (!iframe || !iframe.contentWindow) {
        // this.logger.error('Cannot access iframe content window');
        return;
      }

      // Check if we can access the contentDocument (cross-origin check)
      let isCrossOrigin = false;
      try {
        // This will throw an error if cross-origin
        const testAccess = iframe.contentDocument;
        if (!testAccess) {
          // this.logger.error('Cannot access iframe content document');
          isCrossOrigin = true;
        }
      } catch (e) {
        // this.logger.warn('Cross-origin iframe detected, using postMessage for communication');
        isCrossOrigin = true;
      }

      // If cross-origin, use postMessage to inject scripts
      if (isCrossOrigin) {
        // Send a message to the iframe to initialize element selection
        iframe.contentWindow.postMessage({
          type: 'initElementSelection'
        }, '*');

        // Add event listener for messages from the iframe
        window.addEventListener('message', this.handleIframeMessage);
        return;
      }

      // If we can access the contentDocument directly (same-origin)
      if (!iframe.contentDocument) {
        // this.logger.error('Cannot access iframe content document');
        return;
      }

      // Create a style element for highlighting
      const style = iframe.contentDocument.createElement('style');
      style.id = 'element-selection-styles';
      style.textContent = `
        .element-hover {
          outline: 2px dashed #007bff !important;
          outline-offset: 2px !important;
          cursor: pointer !important;
          position: relative !important;
        }

        .element-selected {
          outline: 3px solid #28a745 !important;
          outline-offset: 3px !important;
          position: relative !important;
        }

        .element-tooltip {
          position: fixed;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          z-index: 10000;
          pointer-events: none;
        }
      `;

      // Add the style to the iframe document
      iframe.contentDocument.head.appendChild(style);

      // Create a script element for the selection logic
      const script = iframe.contentDocument.createElement('script');
      script.id = 'element-selection-script';
      script.textContent = `
        (function() {
          let hoveredElement = null;
          let tooltip = null;

          // Create tooltip element
          function createTooltip() {
            tooltip = document.createElement('div');
            tooltip.className = 'element-tooltip';
            document.body.appendChild(tooltip);
          }

          // Update tooltip position and content
          function updateTooltip(element, event) {
            if (!tooltip) createTooltip();

            const tagName = element.tagName.toLowerCase();
            const id = element.id ? '#' + element.id : '';
            const classes = Array.from(element.classList)
              .filter(cls => cls !== 'element-hover' && cls !== 'element-selected')
              .map(cls => '.' + cls)
              .join('');

            tooltip.textContent = tagName + id + classes;
            tooltip.style.top = (event.clientY + 15) + 'px';
            tooltip.style.left = (event.clientX + 10) + 'px';
          }

          // Hide tooltip
          function hideTooltip() {
            if (tooltip) {
              tooltip.style.display = 'none';
            }
          }

          // Show tooltip
          function showTooltip() {
            if (tooltip) {
              tooltip.style.display = 'block';
            }
          }

          // Handle mouseover event
          function handleMouseOver(event) {
            // Skip if the target is the body or html element
            if (event.target === document.body || event.target === document.documentElement) {
              return;
            }

            // Remove hover class from previous element
            if (hoveredElement && hoveredElement !== event.target) {
              hoveredElement.classList.remove('element-hover');
            }

            // Add hover class to current element
            hoveredElement = event.target;
            hoveredElement.classList.add('element-hover');

            // Update tooltip
            updateTooltip(hoveredElement, event);
            showTooltip();

            // Stop event propagation
            event.stopPropagation();
          }

          // Handle mousemove event
          function handleMouseMove(event) {
            if (hoveredElement) {
              updateTooltip(hoveredElement, event);
            }
          }

          // Handle mouseout event
          function handleMouseOut(event) {
            // Only remove the hover class if we're leaving the element
            if (hoveredElement && !hoveredElement.contains(event.relatedTarget)) {
              hoveredElement.classList.remove('element-hover');
              hoveredElement = null;
              hideTooltip();
            }

            event.stopPropagation();
          }

          // Handle click event
          function handleClick(event) {
            // Skip if the target is the body or html element
            if (event.target === document.body || event.target === document.documentElement) {
              return;
            }

            // Get the clicked element
            const element = event.target;

            // Remove hover class
            element.classList.remove('element-hover');

            // Add selected class
            element.classList.add('element-selected');

            // Get element details
            const tagName = element.tagName.toLowerCase();
            const id = element.id || null;
            const classes = Array.from(element.classList)
              .filter(cls => cls !== 'element-hover' && cls !== 'element-selected')
              .join(' ');

            // Get element HTML
            const outerHTML = element.outerHTML;

            // Get computed styles
            const computedStyle = window.getComputedStyle(element);
            const cssProperties = {};
            for (let i = 0; i < computedStyle.length; i++) {
              const prop = computedStyle[i];
              cssProperties[prop] = computedStyle.getPropertyValue(prop);
            }

            // Get element path
            const getElementPath = (el) => {
              if (!el) return '';
              if (el === document.body) return 'body';

              let path = '';
              let current = el;

              while (current && current !== document.body) {
                let selector = current.tagName.toLowerCase();

                if (current.id) {
                  selector += '#' + current.id;
                } else {
                  const siblings = Array.from(current.parentNode.children)
                    .filter(child => child.tagName === current.tagName);

                  if (siblings.length > 1) {
                    const index = siblings.indexOf(current) + 1;
                    selector += ':nth-of-type(' + index + ')';
                  }
                }

                path = selector + (path ? ' > ' + path : '');
                current = current.parentNode;
              }

              return 'body > ' + path;
            };

            const elementPath = getElementPath(element);

            // Send message to parent window
            window.parent.postMessage({
              type: 'elementSelected',
              data: {
                tagName,
                id,
                classes,
                html: outerHTML,
                css: cssProperties,
                path: elementPath
              }
            }, '*');

            // Stop event propagation and prevent default
            event.stopPropagation();
            event.preventDefault();
          }

          // Add event listeners
          document.addEventListener('mouseover', handleMouseOver, true);
          document.addEventListener('mousemove', handleMouseMove, true);
          document.addEventListener('mouseout', handleMouseOut, true);
          document.addEventListener('click', handleClick, true);

          // Function to clean up element selection
          function cleanupElementSelection() {
            document.removeEventListener('mouseover', handleMouseOver, true);
            document.removeEventListener('mousemove', handleMouseMove, true);
            document.removeEventListener('mouseout', handleMouseOut, true);
            document.removeEventListener('click', handleClick, true);

            // Remove any hover or selected classes
            const hovered = document.querySelector('.element-hover');
            if (hovered) hovered.classList.remove('element-hover');

            const selected = document.querySelector('.element-selected');
            if (selected) selected.classList.remove('element-selected');

            // Remove tooltip
            if (tooltip) {
              document.body.removeChild(tooltip);
              tooltip = null;
            }

            // Remove style and script elements
            const style = document.getElementById('element-selection-styles');
            if (style) style.parentNode.removeChild(style);

            const script = document.getElementById('element-selection-script');
            if (script) script.parentNode.removeChild(script);
          }

          // Store the cleanup function for direct access (same-origin case)
          window._elementSelectionCleanup = cleanupElementSelection;

          // Also listen for cleanup messages (cross-origin case)
          window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'cleanupElementSelection') {

              cleanupElementSelection();
            }
          });
        })();
      `;

      // Add the script to the iframe document
      iframe.contentDocument.body.appendChild(script);

      // Add event listener for messages from the iframe
      window.addEventListener('message', this.handleIframeMessage);

    } catch (error) {
      // this.logger.error('Error injecting selection scripts:', error);
    }
  }

  /**
   * Handles messages from the iframe
   */
  private handleIframeMessage = (event: MessageEvent): void => {
    // Check if the message is from our iframe
    if (event.data && event.data.type === 'elementSelected') {

      // Store the selected element data
      this.selectedElementTagName = event.data.data.tagName;
      this.selectedElementId = event.data.data.id;
      this.selectedElementHTML = event.data.data.html;
      this.selectedElementCSS = JSON.stringify(event.data.data.css, null, 2);
      this.selectedElementPath = event.data.data.path;

      // Add a message to the chat window with the selected element
      this.addSelectedElementToChat(event.data.data);

      // Turn off selection mode
      this.isElementSelectionMode = false;
      this.cleanupSelectionMode();

      // Force change detection
      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });
    }
  };

  /**
   * Adds the selected element to the chat window
   */
  private addSelectedElementToChat(elementData: any): void {
    // Create a simplified HTML representation for display
    const displayHTML = this.formatHTMLForDisplay(elementData.html);

    // Create a message with the selected element
    const message = `## Selected Element: \`<${elementData.tagName}>\`

\`\`\`html
${displayHTML}
\`\`\`

**Element Path:** \`${elementData.path}\`

Would you like to modify this element? Please describe the changes you'd like to make.`;

    // Add the message to the chat
    this.lightMessages.push({
      text: message,
      from: 'ai',
      theme: 'light',
    });

    // Force change detection
    this.cdr.detectChanges();
  }

  /**
   * Formats HTML for display in the chat window
   */
  private formatHTMLForDisplay(html: string): string {
    // Limit the HTML to a reasonable length
    if (html.length > 500) {
      html = html.substring(0, 500) + '...';
    }

    // Add indentation for better readability
    return html
      .replace(/></g, '>\n<')
      .replace(/(<[^\/].*?>)/g, '$1')
      .replace(/(<\/.*?>)/g, '$1');
  }

  /**
   * Cleans up selection mode
   */
  private cleanupSelectionMode(): void {
    try {
      // Get the iframe element
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (!iframe || !iframe.contentWindow) {
        return;
      }

      // For cross-origin iframes, we can't directly access properties
      // Instead, send a message to the iframe to clean up
      try {
        // First try direct access (same-origin case)
        if (iframe.contentWindow && 'function' === typeof (iframe.contentWindow as any)._elementSelectionCleanup) {
          (iframe.contentWindow as any)._elementSelectionCleanup();
        } else {
          // If direct access fails or isn't available, use postMessage
          iframe.contentWindow.postMessage({ type: 'cleanupElementSelection' }, '*');
          this.logger.debug('Sent cleanup message to iframe');
        }
      } catch (crossOriginError) {
        // If we get a cross-origin error, use postMessage as fallback
        // this.logger.info('Using postMessage fallback for cross-origin iframe cleanup');
        iframe.contentWindow.postMessage({ type: 'cleanupElementSelection' }, '*');
      }

      // Remove the event listener
      window.removeEventListener('message', this.handleIframeMessage);

      // this.logger.info('Selection mode cleaned up');
    } catch (error) {
      // this.logger.error('Error cleaning up selection mode:', error);
    }
  }

  /**
   * Clears the selected element
   */
  private clearSelectedElement(): void {
    this.selectedElementTagName = null;
    this.selectedElementId = null;
    this.selectedElementHTML = null;
    this.selectedElementCSS = null;
    this.selectedElementPath = null;
    this.selectedElement = null;
  }

  /**
   * Handles edit button click to toggle element selection mode
   */
  onEditButtonClick(): void {
    // Toggle element selection mode
    this.toggleElementSelectionMode();
  }

  /**
   * Handles sending a message with the selected element modification request
   * @param message The user's message describing the desired modifications
   */
  sendElementModificationRequest(message: string): void {
    if (!this.selectedElementHTML || !this.selectedElementPath) {
      // this.logger.error('No element selected for modification');
      return;
    }

    // Get user signature
    const userSignature = this.userSignatureService.getUserSignatureSync();
    // this.logger.debug('Using user signature for element modification:', userSignature);

    // Create the payload for the API call
    const payload = {
      elementHtml: this.selectedElementHTML,
      elementCss: this.selectedElementCSS,
      elementPath: this.selectedElementPath,
      userMessage: message,
      projectId: this.projectId,
      jobId: this.jobId,
      userSignature: userSignature
    };

    // this.logger.debug('Sending element modification request:', payload);

    // Add user message to chat
    this.lightMessages.push({
      text: message,
      from: 'user',
      theme: 'light',
    });

    // Add a loading message from AI
    this.lightMessages.push({
      text: "I'm processing your request to modify the selected element. This may take a moment...",
      from: 'ai',
      theme: 'light',
    });

    // Make the API call to modify the element
    this.codeGenerationService.modifyElement(payload).subscribe({
      next: response => {
        // Update the last AI message with the response
        const lastAiMessage = this.lightMessages.find(msg => msg.from === 'ai');
        if (lastAiMessage) {
          lastAiMessage.text = `I've updated the element based on your request. Here's what I did:

\`\`\`html
${this.formatHTMLForDisplay(response.modifiedHtml || '')}
\`\`\`

The changes should be reflected in the preview shortly.`;
        }

        // Clear the selected element
        this.clearSelectedElement();

        // Force change detection
        this.cdr.detectChanges();
      },
      error: error => {
        // this.logger.error('Error modifying element:', error);

        // Update the last AI message with the error
        const lastAiMessage = this.lightMessages.find(msg => msg.from === 'ai');
        if (lastAiMessage) {
          lastAiMessage.text = `I encountered an error while trying to modify the element. Please try again or select a different element.`;
        }

        // Force change detection
        this.cdr.detectChanges();
      },
    });
  }

  // Method to handle editor icon click (legacy method, kept for compatibility)
  onEditorIconClick() {
    // Toggle element selection mode
    this.toggleElementSelectionMode();
  }

  /**
   * Handle retry button click from error page
   * Restarts the stepper from where it left off and restarts polling
   */
  onRetryClick() {
    // Reset error state using BehaviorSubject
    this.previewError$.next(false);

    // Reset code generation complete flag to hide code tab and show loading state
    this.isCodeGenerationComplete = false;

    // Reset code tab enabled flag
    this.isCodeTabEnabled = false;

    // Reset prompt bar enabled flag
    this.isPromptBarEnabled = false;

    // Ensure code tab is not active when retrying using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isLogsActive$.next(false);

    // Show toast notification
    this.toastService.info('Retrying code generation...');

    // If we have project ID and job ID, restart polling
    // 🛡️ CRITICAL: Block polling service in UI Design mode
    if (this.projectId && this.jobId && !this.isUIDesignMode$.value) {
      // Get the current step from the polling service
      const currentStep = this.pollingService.getCurrentStep();
      // const lastProgressDescription = this.pollingService.getLastProgressDescription(); // Unused for now

      // Reset the polling service but keep the current step
      this.pollingService.resetLogsButKeepStep();

      // Start polling again with the same project and job IDs
      this.pollingService.startPolling(this.projectId, this.jobId, {
        taskType: 'code generation retry',
        initialInterval: 1000,
        maxInterval: 20000,
        backoffFactor: 1.5,
        startFromStep: currentStep // Pass the current step to resume from
      });
    } else if (this.isUIDesignMode$.value) {
      this.logger.info('🚫 Blocking retry polling service in UI Design mode - maintaining isolation');
    }

    if (this.projectId && this.jobId) {
      this.updatePollingStatus(true);

      // Show loading animation using BehaviorSubjects
      this.isLoading$.next(true);
      this.isPreviewLoading$.next(true);

      // Switch to preview view
      this.togglePreviewView();
    } else {
      // If we don't have project ID and job ID, navigate to home
      this.navigateToHome();
    }
  }

  /**
   * Highlights an element in the iframe using a CSS selector
   * This method is kept for future use but is currently unused
   * @param selector CSS selector to find the element
   */
  // private highlightElementInIframe(selector: string) {
  //   const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
  //   if (iframe?.contentDocument) {
  //     const element = iframe.contentDocument.querySelector(selector);
  //     if (element) {
  //       element.classList.add(this.lastSelectedElementClass);
  //     }
  //   }
  // }

  /**
   * Sorts files based on the selected technology (React, Angular, Vue.js)
   * @param files Array of file models to sort
   * @param technology The selected technology ('react', 'angular', 'vue')
   * @returns Sorted array of file models
   */
  private sortFilesByTechnology(files: FileModel[], technology: string): FileModel[] {
    if (!files || files.length === 0) {
      return files;
    }

    // Define file priority based on technology (reversed priority - higher number = higher priority)
    const filePriority: { [key: string]: { [key: string]: number } } = {
      react: {
        // Source files (highest priority)
        'src/components/': 100,
        'components/': 100,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/hooks/': 96,
        'hooks/': 96,
        'src/context/': 95,
        'context/': 95,
        'src/assets/': 94,
        'assets/': 94,
        'src/styles/': 93,
        'styles/': 93,
        'src/App.js': 92,
        'src/App.jsx': 92,
        'src/App.tsx': 92,
        'App.js': 92,
        'App.jsx': 92,
        'App.tsx': 92,
        'src/App.css': 91,
        'App.css': 91,
        'src/index.js': 90,
        'src/index.jsx': 90,
        'src/index.tsx': 90,
        'index.js': 90,
        'index.jsx': 90,
        'index.tsx': 90,
        'public/index.html': 89,
        'index.html': 89,
        'public/': 88,
        '.js': 80,
        '.jsx': 80,
        '.tsx': 80,
        '.css': 79,
        '.scss': 79,
        '.html': 78,

        // Configuration files (lowest priority)
        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
      angular: {
        // Source files (highest priority)
        'src/app/components/': 100,
        'components/': 100,
        'src/app/pages/': 99,
        'pages/': 99,
        'src/app/services/': 98,
        'services/': 98,
        'src/app/models/': 97,
        'models/': 97,
        'src/app/directives/': 96,
        'directives/': 96,
        'src/app/pipes/': 95,
        'pipes/': 95,
        'src/app/guards/': 94,
        'guards/': 94,
        'src/app/interceptors/': 93,
        'interceptors/': 93,
        'src/assets/': 92,
        'assets/': 92,
        'src/environments/': 91,
        'environments/': 91,
        'src/app/app.component.ts': 90,
        'app.component.ts': 90,
        'src/app/app.component.html': 89,
        'app.component.html': 89,
        'src/app/app.component.scss': 88,
        'src/app/app.component.css': 88,
        'app.component.scss': 88,
        'app.component.css': 88,
        'src/app/app-routing.module.ts': 87,
        'app-routing.module.ts': 87,
        'src/app/app.module.ts': 86,
        'app.module.ts': 86,
        'src/main.ts': 85,
        'main.ts': 85,
        'src/index.html': 84,
        'index.html': 84,
        '.ts': 80,
        '.html': 79,
        '.scss': 78,
        '.css': 78,

        // Configuration files (lowest priority)
        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
      vue: {
        // Source files (highest priority)
        'src/components/': 100,
        'components/': 100,
        'src/views/': 99,
        'views/': 99,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/assets/': 96,
        'assets/': 96,
        'src/styles/': 95,
        'styles/': 95,
        'src/store/index.js': 94,
        'src/store/index.ts': 94,
        'store/index.js': 94,
        'store/index.ts': 94,
        'src/router/index.js': 93,
        'src/router/index.ts': 93,
        'router/index.js': 93,
        'router/index.ts': 93,
        'src/App.vue': 92,
        'App.vue': 92,
        'src/main.js': 91,
        'src/main.ts': 91,
        'main.js': 91,
        'main.ts': 91,
        'public/index.html': 90,
        'index.html': 90,
        'public/': 89,
        '.vue': 80,
        '.js': 79,
        '.ts': 79,
        '.scss': 78,
        '.css': 78,
        '.html': 77,

        // Configuration files (lowest priority)
        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
    };

    // Normalize technology name
    const normalizedTech = technology.toLowerCase();
    const techKey = normalizedTech.includes('react')
      ? 'react'
      : normalizedTech.includes('angular')
        ? 'angular'
        : normalizedTech.includes('vue')
          ? 'vue'
          : 'angular';

    // Get the priority map for the selected technology
    const priorityMap = filePriority[techKey] || filePriority['angular']; // Default to Angular if not found

    // Sort the files based on priority
    const sortedFiles = [...files].sort((a, b) => {
      const aName = a.name.toLowerCase();
      const bName = b.name.toLowerCase();

      // Get priority for file A
      let aPriority = 1000; // Default high number for low priority
      let aNestedLevel = aName.split('/').length - 1; // Calculate nesting level

      for (const [pattern, priority] of Object.entries(priorityMap)) {
        if (pattern.endsWith('/')) {
          // Handle directory patterns
          if (aName.includes(pattern)) {
            aPriority = priority;
            break;
          }
        } else if (aName === pattern.toLowerCase()) {
          // Exact match
          aPriority = priority;
          break;
        } else if (aName.endsWith(pattern.toLowerCase())) {
          // Match file extension or end of path
          aPriority = priority;
          break;
        }
      }

      // Get priority for file B
      let bPriority = 1000; // Default high number for low priority
      let bNestedLevel = bName.split('/').length - 1; // Calculate nesting level

      for (const [pattern, priority] of Object.entries(priorityMap)) {
        if (pattern.endsWith('/')) {
          // Handle directory patterns
          if (bName.includes(pattern)) {
            bPriority = priority;
            break;
          }
        } else if (bName === pattern.toLowerCase()) {
          // Exact match
          bPriority = priority;
          break;
        } else if (bName.endsWith(pattern.toLowerCase())) {
          // Match file extension or end of path
          bPriority = priority;
          break;
        }
      }

      // Sort by priority first (higher number = higher priority)
      if (aPriority !== bPriority) {
        return bPriority - aPriority; // Reversed comparison for reversed priority
      }

      // If same priority, sort by nesting level (higher nesting level gets higher priority)
      if (aNestedLevel !== bNestedLevel) {
        return bNestedLevel - aNestedLevel; // Reverse order to prioritize deeper nesting
      }

      // If same nesting level, sort alphabetically
      return aName.localeCompare(bName);
    });

    // Process files to analyze nesting levels (for future use)
    // Commented out to avoid unused variable warning
    // sortedFiles.forEach(file => {
    //   const nestedLevel = file.name.split('/').length - 1;
    //   Process nesting level if needed in the future
    // });

    // Calculate nesting statistics (for future use)
    const nestingStats: Record<string, number> = {};
    sortedFiles.forEach(file => {
      const level = file.name.split('/').length - 1;
      const levelKey = level.toString();
      nestingStats[levelKey] = (nestingStats[levelKey] || 0) + 1;
    });

    // Process nesting statistics if needed in the future
    // Object.entries(nestingStats).forEach(([_level, _count]) => {
    //   // Process statistics if needed
    // });

    return sortedFiles;
  }

  /**
   * Initialize the chat with a default prompt if no prompt is available from the app state
   * This ensures that the user sees their original prompt in the chat window
   */
  private initializeDefaultPrompt(): void {
    // Only initialize if the lightMessages array is empty
    if (this.lightMessages.length === 0) {
      // Get the prompt from the app state if available
      this.appStateService.project$
        .subscribe(projectState => {
          if (projectState.prompt) {
            // Create a summary of user selections
            let userSelectionsSummary = '';

            // Add image information if available
            if (projectState.imageUrl) {
              userSelectionsSummary += '- **Image**:  You provided an image for reference\n';
            }

            // Add technology information if available
            if (projectState.technology) {
              userSelectionsSummary += `- **Technology**: ${projectState.technology.charAt(0).toUpperCase() + projectState.technology.slice(1)}\n`;
            }

            // Add design library information if available
            if (projectState.designLibrary) {
              userSelectionsSummary += `- **Design Library**: ${projectState.designLibrary.charAt(0).toUpperCase() + projectState.designLibrary.slice(1)}\n`;
            }

            // Add application type information if available
            if (projectState.application) {
              userSelectionsSummary += `- **Application Type**: ${projectState.application.charAt(0).toUpperCase() + projectState.application.slice(1)}\n`;
            }

            // Add type information if available
            if (projectState.type) {
              userSelectionsSummary += `- **Generation Type**: ${projectState.type}\n`;
            }

            // Add the user prompt to the chat messages with a comprehensive project overview
            this.lightMessages = [
              {
                text: projectState.prompt,
                from: 'user',
                theme: 'light',
                imageDataUri: projectState.imageDataUri || undefined
              },
//               {
//                 text: `# Project Overview
// ## Your Selections
// ${userSelectionsSummary.trim() ? userSelectionsSummary : '**No additional selections provided**'}.`,
//                 from: 'ai',
//                 theme: 'light',
//               },
            ];

            // Also store the image data URI for the chat window component
            if (projectState.imageDataUri) {
              this.selectedImageDataUri = projectState.imageDataUri;
            }
          } else {
            // If no prompt is available, add a default message
            this.lightMessages = [
              {
                text: "Welcome to the code generation experience. I'll help you create code based on your requirements.",
                from: 'ai',
                theme: 'light',
              },
            ];
          }
        })
        .unsubscribe(); // Unsubscribe immediately since we only need the current value
    }
  }

  /**
   * Starts streaming logs with immediate display (no typewriter effect)
   * UPDATED: Removed typewriter effect, show full content immediately
   * @param allLogs Array of all log messages from the API
   */
  private startLogStreaming(allLogs: string[]): void {
    // Clear any existing timer
    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
    }

    // If no logs, don't start streaming
    if (!allLogs || allLogs.length === 0) {
      this.isStreamingLogs = false;
      return;
    }

    // Initialize streaming state if this is the first time
    const wasFirstTime = !this.hasLogs;
    if (!this.hasLogs) {
      this.hasLogs = true;
    }

    // Auto-enable logs tab when logs become available for the first time
    if (wasFirstTime) {
      this.autoEnableLogsTabIfNeeded();
    }

    // Filter out logs we've already processed to avoid duplicates
    const newLogs = this.filterNewLogs(allLogs);

    if (newLogs.length === 0) {
      this.isStreamingLogs = false;
      return;
    }

    // this.logger.debug(`Processing ${newLogs.length} new logs out of ${allLogs.length} total logs`);

    // Process and format only the new logs
    const processedLogs = this.processLogs(newLogs);

    if (processedLogs.length > 0) {
      // Set streaming state to false since we're showing content immediately
      this.isStreamingLogs = false;
      this.isTypingLog = false;

      // Show full content immediately for all new logs
      processedLogs.forEach(log => {
        log.visibleContent = log.content || '';
      });

      // Append the new processed logs to our existing formatted logs array
      this.formattedLogMessages = [...this.formattedLogMessages, ...processedLogs];

      // Keep the original logs for compatibility
      this.logMessages = [...this.logMessages, ...newLogs];

      // Trigger change detection and scroll to bottom
      this.cdr.detectChanges();
      this.scrollLogsToBottom();
    }
  }

  /**
   * Filter out logs that have already been processed to avoid duplicates
   * UPDATED: Since we're now receiving accumulated logs, we need to handle this differently
   * @param allLogs All logs from the API
   * @returns Only the new logs that haven't been processed yet
   */
  private filterNewLogs(allLogs: string[]): string[] {
    // Since we're now receiving accumulated logs, we need to check against our existing formatted logs
    const existingLogContent = this.formattedLogMessages.map(log => log.content || '').join('\n');

    return allLogs.filter(log => {
      // Skip empty logs
      if (!log || log.trim() === '') {
        return false;
      }

      // Check if this log content already exists in our formatted logs
      if (existingLogContent.includes(log.trim())) {
        return false;
      }

      // Create a simple hash of the log to use as a unique identifier
      const logHash = this.createLogHash(log);

      // If we've already processed this log, skip it
      if (this.processedLogHashes.has(logHash)) {
        return false;
      }

      // Otherwise, add it to our set of processed logs and include it
      this.processedLogHashes.add(logHash);
      return true;
    });
  }

  /**
   * Create a simple hash of a log message to use as a unique identifier
   * @param log The log message
   * @returns A string hash
   */
  private createLogHash(log: string): string {
    // For simplicity, we'll use the first 100 characters of the log as a hash
    // In a production environment, you might want to use a more robust hashing algorithm
    return log.substring(0, 100);
  }

  /**
   * Process logs to extract code blocks and format them properly
   * @param logs Array of raw log messages
   * @returns Array of formatted log objects
   */
  private processLogs(logs: string[]): any[] {
    const processedLogs: any[] = [];
    // Track logs that have been processed as JSON to avoid duplicates
    const processedJsonLogs = new Set<string>();

    // this.logger.debug('Processing logs:', logs.length);

    logs.forEach(log => {
      // Skip empty logs
      if (!log || log.trim() === '') {
        return;
      }

      // Extract the log content for state change detection
      const logContent = this.extractLogContent(log);

      // Check if this is a log containing code (key-value pair format)
      // Special handling for logs that contain "Generated code:" marker
      if (log.includes('Generated code:')) {
        try {
          // Extract the JSON part after "Generated code:"
          const jsonStartIndex = log.indexOf('Generated code:') + 'Generated code:'.length;
          const jsonPart = log.substring(jsonStartIndex).trim();
          // this.logger.debug('Found log with Generated code marker, JSON length:', jsonPart.length);

          if (jsonPart) {
            const codeData = JSON.parse(jsonPart);
            // this.logger.debug('Successfully parsed code data from Generated code log');

            // Mark this log as processed to avoid showing the raw JSON
            processedJsonLogs.add(log);

            // Process the code data directly
            if (typeof codeData === 'object' && codeData !== null) {
              if (Array.isArray(codeData)) {
                // Handle array of files
                codeData.forEach(item => {
                  if (item && item.fileName && item.content) {
                    const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                    processedLogs.push({
                      id: codeId,
                      type: 'code',
                      timestamp: this.extractTimestamp(log),
                      path: item.fileName,
                      content: item.content,
                      contentLines: item.content.split('\n').length,
                      contentSize: item.content.length,
                      rawLog: log,
                    });

                    // Auto-expand code logs
                    this.expandedCodeLogs.add(codeId);
                  }
                });
                return;
              } else {
                // Handle object with file paths as keys
                Object.entries(codeData).forEach(([path, content]) => {
                  if (path && content) {
                    const formattedContent =
                      typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                    const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                    processedLogs.push({
                      id: codeId,
                      type: 'code',
                      timestamp: this.extractTimestamp(log),
                      path: path,
                      content: formattedContent,
                      contentLines: formattedContent.split('\n').length,
                      contentSize: formattedContent.length,
                      rawLog: log,
                    });

                    // Auto-expand code logs
                    this.expandedCodeLogs.add(codeId);
                  }
                });
                return;
              }
            }
          }
        } catch (e) {
          // this.logger.error('Error processing Generated code log:', e);
        }
      }

      // Regular JSON detection in logs
      if (log.includes('{') && log.includes('}')) {
        try {
          // Try to extract JSON from the log
          const jsonStartIndex = log.indexOf('{');
          const jsonEndIndex = log.lastIndexOf('}') + 1;

          if (jsonStartIndex !== -1 && jsonEndIndex > jsonStartIndex) {
            const jsonPart = log.substring(jsonStartIndex, jsonEndIndex);
            this.logger.debug('Found potential JSON in log:', jsonPart.substring(0, 100) + '...');

            const codeData = JSON.parse(jsonPart);
            // this.logger.debug(
            //   'Parsed code data type:',
            //   typeof codeData,
            //   Array.isArray(codeData) ? 'array' : ''
            // );

            // Mark this log as processed to avoid showing the raw JSON
            processedJsonLogs.add(log);

            // Special handling for logs with filesToGenerate array
            if (codeData && typeof codeData === 'object' && codeData.filesToGenerate && Array.isArray(codeData.filesToGenerate)) {
              // this.logger.debug('Found filesToGenerate array in log:', codeData.filesToGenerate);

              // Format the files list as a nicely formatted string with file type icons
              const formattedFiles = codeData.filesToGenerate.map((file: string) => {
                // Add file extension icon based on file type
                let icon = '📄';
                if (file.endsWith('.ts') || file.endsWith('.tsx')) icon = '📘';
                else if (file.endsWith('.js') || file.endsWith('.jsx')) icon = '📙';
                else if (file.endsWith('.css')) icon = '🎨';
                else if (file.endsWith('.html')) icon = '🌐';
                else if (file.endsWith('.json')) icon = '📋';

                return `- ${icon} ${file}`;
              });

              const formattedContent = `# Files to Generate\n\n${formattedFiles.join('\n')}`;

              // Skip if formatted content is empty
              if (!formattedContent || formattedContent.trim() === '') return;

              // Add the code log with a unique ID
              const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
              processedLogs.push({
                id: codeId,
                type: 'code',
                timestamp: this.extractTimestamp(log),
                path: 'Project Structure',
                content: formattedContent,
                contentLines: formattedContent.split('\n').length,
                contentSize: formattedContent.length,
                rawLog: log,
              });

              // Auto-expand this code log
              this.expandedCodeLogs.add(codeId);

              // Also add a regular log message to indicate what's happening
              processedLogs.push({
                type: 'info',
                timestamp: this.extractTimestamp(log),
                content: `Preparing to generate ${codeData.filesToGenerate.length} files`,
                rawLog: log,
              });

              return;
            }

            // Special handling for logs with message and data fields where data is a JSON string containing code
            if (codeData && typeof codeData === 'object' && codeData.message && codeData.data) {
              // this.logger.debug('Found log with message and data fields:', codeData.message);

              // Check if data is a string that might contain code
              if (typeof codeData.data === 'string') {
                // Handle the data field which might be a JSON string
                let dataString = codeData.data;
                let dataJson = null;

                // First try to parse it directly
                try {
                  dataJson = JSON.parse(dataString);
                } catch (directParseError) {
                  // If direct parsing fails and the string contains escaped characters, try to unescape it
                  if (dataString.includes('\\\"') || dataString.includes('\\\\')) {
                    try {
                      // Replace escaped quotes and backslashes
                      dataString = dataString.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                      dataJson = JSON.parse(dataString);
                    } catch (unescapeError) {
                    }
                  }
                }

                // If we successfully parsed the data field as JSON
                if (dataJson) {
                  // Handle different formats of the data field
                  if (typeof dataJson === 'object' && !Array.isArray(dataJson)) {
                    // Object with file paths as keys
                    // Process each file path and content
                    Object.entries(dataJson).forEach(([path, content]) => {
                      if (path && content) {
                        const formattedContent =
                          typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                        // Skip if formatted content is empty
                        if (!formattedContent || formattedContent.trim() === '') return;

                        const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                        processedLogs.push({
                          id: codeId,
                          type: 'code',
                          timestamp: this.extractTimestamp(log),
                          path: path,
                          content: formattedContent,
                          contentLines: formattedContent.split('\n').length,
                          contentSize: formattedContent.length,
                          rawLog: log,
                        });

                        // Auto-expand code logs
                        this.expandedCodeLogs.add(codeId);
                      }
                    });
                  } else if (Array.isArray(dataJson)) {
                    dataJson.forEach(item => {
                      if (item && typeof item === 'object') {
                        const path = item.fileName || item.path || item.name || 'unknown.file';
                        const content = item.content || '';

                        if (path && content) {
                          const formattedContent =
                            typeof content === 'string'
                              ? content
                              : JSON.stringify(content, null, 2);

                          // Skip if formatted content is empty
                          if (!formattedContent || formattedContent.trim() === '') return;

                          const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                          processedLogs.push({
                            id: codeId,
                            type: 'code',
                            timestamp: this.extractTimestamp(log),
                            path: path,
                            content: formattedContent,
                            contentLines: formattedContent.split('\n').length,
                            contentSize: formattedContent.length,
                            rawLog: log,
                          });

                          // Auto-expand code logs
                          this.expandedCodeLogs.add(codeId);
                        }
                      }
                    });
                  }

                  // Also add the original message as an info log
                  processedLogs.push({
                    type: 'info',
                    timestamp: this.extractTimestamp(log),
                    content: codeData.message,
                    rawLog: log,
                  });

                  return;
                }
              }
            }

            // Check if this is a key-value pair format (path: content)
            if (typeof codeData === 'object' && codeData !== null) {
              // Handle object format with key-value pairs
              if (!Array.isArray(codeData)) {
                // Process each key-value pair as a separate code block
                Object.entries(codeData).forEach(([path, content]) => {

                  // Skip if content is empty
                  if (!content || (typeof content === 'string' && content.trim() === '')) {
                    return;
                  }

                  // Skip empty content
                  if (!content) return;

                  // Format the content properly
                  const formattedContent =
                    typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                  // Skip if formatted content is empty or just whitespace
                  if (!formattedContent || formattedContent.trim() === '') return;

                  // Calculate content size for proper capsule sizing
                  const contentLines = formattedContent.split('\n').length;
                  const contentSize = formattedContent.length;

                  // Add the code log with a unique ID
                  const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  processedLogs.push({
                    id: codeId,
                    type: 'code',
                    timestamp: this.extractTimestamp(log),
                    path: path || 'unknown.file',
                    content: formattedContent,
                    contentLines: contentLines,
                    contentSize: contentSize,
                    rawLog: log,
                  });

                  // Auto-expand code logs
                  this.expandedCodeLogs.add(codeId);
                });
                return;
              }
              // Handle array format with fileName and content properties
              else if (Array.isArray(codeData) && codeData.length > 0) {
                codeData.forEach(item => {
                  // Skip if content is empty
                  if (
                    !item.content ||
                    (typeof item.content === 'string' && item.content.trim() === '')
                  ) {
                    return;
                  }

                  // Format the content properly
                  const formattedContent =
                    typeof item.content === 'string'
                      ? item.content
                      : JSON.stringify(item.content, null, 2);

                  // Skip if formatted content is empty
                  if (!formattedContent || formattedContent.trim() === '') return;

                  // Add the code log with a unique ID
                  const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  processedLogs.push({
                    id: codeId,
                    type: 'code',
                    timestamp: this.extractTimestamp(log),
                    path: item.fileName || item.path || 'unknown.file',
                    content: formattedContent,
                    contentLines: formattedContent.split('\n').length,
                    contentSize: formattedContent.length,
                    rawLog: log,
                  });

                  // Auto-expand code logs
                  this.expandedCodeLogs.add(codeId);
                });
                return;
              }
              // Handle single file format with fileName and content properties
              else if (
                typeof codeData === 'object' &&
                'fileName' in codeData &&
                'content' in codeData
              ) {
                // Skip if content is empty
                if (
                  !codeData.content ||
                  (typeof codeData.content === 'string' && codeData.content.trim() === '')
                ) {
                  return;
                }

                // Format the content properly
                const formattedContent =
                  typeof codeData.content === 'string'
                    ? codeData.content
                    : JSON.stringify(codeData.content, null, 2);

                // Skip if formatted content is empty or just whitespace
                if (!formattedContent || formattedContent.trim() === '') return;

                // Calculate content size for proper capsule sizing
                const contentLines = formattedContent.split('\n').length;
                const contentSize = formattedContent.length;

                // Add the code log with a unique ID
                const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                processedLogs.push({
                  id: codeId,
                  type: 'code',
                  timestamp: this.extractTimestamp(log),
                  path: codeData.fileName || 'unknown.file',
                  content: formattedContent,
                  contentLines: contentLines,
                  contentSize: contentSize,
                  rawLog: log,
                });

                // Auto-expand code logs
                this.expandedCodeLogs.add(codeId);
                return;
              }
            }
          }
        } catch (e) {
          // this.logger.error('Error parsing code from log:', e);
        }
      }

      // Check for error logs - always show errors
      if (log.includes('ERROR')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          type: 'error',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // Check for warning logs - always show warnings
      if (log.includes('WARN')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          type: 'warning',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // Check for progress description logs - only show if there's a state change
      if (log.includes('Progress Description')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        // Extract the actual progress description
        const progressDesc = logContent.replace('Progress Description Updated: ', '').trim();

        // Check if this is a new state
        if (progressDesc !== this.lastProgressState) {
          this.lastProgressState = progressDesc;

          processedLogs.push({
            type: 'progress-description',
            timestamp: this.extractTimestamp(log),
            content: logContent,
            rawLog: log,
          });
        }
        return;
      }

      // Check for status change logs - always show status changes
      if (log.includes('Status changed to:')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          type: 'status-change',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // For debug logs, only show if they contain useful information
      if (log.includes('DEBUG')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        // Skip raw response logs entirely
        if (logContent.includes('Raw response:')) {
          return;
        }

        // Only include debug logs that have actual content
        processedLogs.push({
          type: 'debug',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // For info logs, only include if they have meaningful content and haven't been processed as JSON
      if (!logContent || logContent.trim() === '' || processedJsonLogs.has(log)) {
        return;
      }

      // Skip logs that contain raw JSON data that we've already processed
      if ((log.includes('{') && log.includes('}')) || (log.includes('[') && log.includes(']'))) {
        // Check if this log contains JSON that we might have already processed
        // This helps filter out the raw JSON logs when we've already processed them as structured data
        const jsonStartIndex = log.indexOf('{');
        const jsonEndIndex = log.lastIndexOf('}') + 1;

        if (jsonStartIndex !== -1 && jsonEndIndex > jsonStartIndex) {
          try {
            // Try to parse the JSON to see if it's valid
            const jsonPart = log.substring(jsonStartIndex, jsonEndIndex);
            JSON.parse(jsonPart);

            // If we get here, it's valid JSON - check if we've already processed a similar log
            // Skip this log if it looks like raw JSON data
            if (jsonPart.includes('"data"') || jsonPart.includes('"filesToGenerate"') ||
                jsonPart.includes('"fileName"') || jsonPart.includes('"content"')) {
              return;
            }
          } catch (e) {
            // Not valid JSON, continue processing as a normal log
          }
        }
      }

      // Default to info log
      processedLogs.push({
        type: 'info',
        timestamp: this.extractTimestamp(log),
        content: logContent,
        rawLog: log,
      });
    });

    // Log the number of processed logs by type
    const logTypes = processedLogs.reduce((acc, log) => {
      acc[log.type] = (acc[log.type] || 0) + 1;
      return acc;
    }, {});

    // this.logger.debug('Processed logs by type:', logTypes);
    // this.logger.debug('Code logs:', processedLogs.filter(log => log.type === 'code').length);

    return processedLogs;
  }

  /**
   * Extract timestamp from a log message
   * @param log The log message
   * @returns The extracted timestamp
   */
  private extractTimestamp(log: string): string {
    // Format: "HH:MM:SS.mmm - LEVEL - Message"
    const parts = log.split(' - ');
    if (parts.length >= 1) {
      return parts[0];
    }
    return '';
  }

  /**
   * Extract the content part from a log message
   * @param log The log message
   * @returns The extracted content
   */
  private extractLogContent(log: string): string {
    // Format: "HH:MM:SS.mmm - LEVEL - Message"
    const parts = log.split(' - ');
    if (parts.length >= 3) {
      return parts.slice(2).join(' - ');
    }
    return log;
  }

  /**
   * Get the language from a file path for syntax highlighting
   */
  private getLanguageFromPath(path: string): string {
    const extension = path.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'ts':
        return 'typescript';
      case 'js':
        return 'javascript';
      case 'html':
        return 'html';
      case 'css':
        return 'css';
      case 'scss':
        return 'scss';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'py':
        return 'python';
      case 'java':
        return 'java';
      case 'xml':
        return 'xml';
      case 'yaml':
      case 'yml':
        return 'yaml';
      default:
        return 'plaintext';
    }
  }

  /**
   * Extract file name from a file path
   */
  private extractFileName(path: string): string {
    if (!path) return 'Unknown file';

    // Handle both forward and backward slashes
    const parts = path.split(/[/\\]/);
    return parts[parts.length - 1] || 'Unknown file';
  }

  /**
   * Start the letter-by-letter typing animation for logs
   * @param startIndex Optional index to start typing from (for appending new logs)
   */
  private startLetterByLetterTyping(startIndex: number = 0): void {
    // Set initial state
    this.isTypingLog = true;
    this.isStreamingLogs = true;
    this.currentLogIndex = startIndex;
    this.currentCharIndex = 0;

    // Initialize visibleContent property for each log if it doesn't exist
    for (let i = startIndex; i < this.formattedLogMessages.length; i++) {
      if (!this.formattedLogMessages[i].visibleContent) {
        this.formattedLogMessages[i].visibleContent = '';
      }
    }

    // Create a deep copy of the formatted logs to avoid modifying the original array
    // For existing logs, preserve their visible content
    const visibleLogs = this.formattedLogMessages.map((log, index) => {
      if (index < startIndex) {
        // For logs we've already processed, keep their visible content
        return {
          ...log,
          visibleContent: log.visibleContent || log.content || '',
        };
      } else {
        // For new logs, start with empty visible content
        return {
          ...log,
          visibleContent: '',
        };
      }
    });

    // For code logs, ensure they have IDs and are expanded by default
    visibleLogs.forEach((log, index) => {
      if (log.type === 'code' && index >= startIndex) {
        // For code logs, we'll use the typewriter effect with a slightly slower speed
        // to make it more visible
        log.visibleContent = ''; // Start with empty content for animation

        // Ensure the log has an ID
        if (!log.id) {
          log.id = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        }

        // Calculate appropriate max-height based on content size
        // This ensures the capsule expands enough to show all content
        if (log.contentLines) {
          // Set a custom data attribute for line count to use in CSS
          log.maxHeight = Math.min(Math.max(log.contentLines * 20, 100), 800); // 20px per line, min 100px, max 800px
        } else {
          // Default max height if we don't have line count
          log.maxHeight = 500;
        }

        // Auto-expand ALL code blocks to show the typewriting effect
        this.expandedCodeLogs.add(log.id);
      }
    });

    // Update the formatted logs array with the initial state
    this.formattedLogMessages = [...visibleLogs];

    // Much slower typing speeds for a highly visible typewriter effect
    // Very slow typing creates a dramatic character-by-character an  imation
    const baseTypingSpeed = 80; // 80ms per character for regular logs (much slower for high visibility)
    const codeTypingSpeed = 60; // 60ms per character for code logs (slightly faster than regular logs but still very visible)

    // Use requestAnimationFrame for smoother animation instead of setInterval
    const animateTyping = () => {
      // Call the typing step method which handles all the logic
      this.typingStep();

      // If we've processed all logs, stop the animation
      if (this.currentLogIndex >= this.formattedLogMessages.length) {
        this.isTypingLog = false;
        this.isStreamingLogs = false;
        return; // Stop the animation loop
      }

      // Determine the delay based on the current log type
      const delay = this.formattedLogMessages[this.currentLogIndex]?.type === 'code'
        ? codeTypingSpeed
        : baseTypingSpeed;

      // Schedule the next frame with the appropriate delay
      setTimeout(() => {
        requestAnimationFrame(animateTyping);
      }, delay);
    };

    // Start the animation loop
    requestAnimationFrame(animateTyping);
  }

  /**
   * Single step of the typing animation, extracted to a separate method
   * so it can be called with different intervals
   */
  private typingStep(): void {
    // If we've processed all logs, stop the animation
    if (this.currentLogIndex >= this.formattedLogMessages.length) {
      if (this.logStreamTimer) {
        clearInterval(this.logStreamTimer);
      }
      this.isTypingLog = false;
      this.isStreamingLogs = false;
      this.cdr.detectChanges(); // Ensure UI is updated
      return;
    }

    // Get the current log we're typing
    const currentLog = this.formattedLogMessages[this.currentLogIndex];
    const currentContent = currentLog.content || '';

    // Ensure visibleContent property exists
    if (!currentLog.visibleContent) {
      currentLog.visibleContent = '';
    }

    // If we're done typing this log, move to the next one
    if (this.currentCharIndex >= currentContent.length) {
      // For code logs, ensure the code capsule is expanded
      if (currentLog.type === 'code' && currentLog.id) {
        // Make sure the code capsule is fully expanded
        if (!this.expandedCodeLogs.has(currentLog.id)) {
          this.expandedCodeLogs.add(currentLog.id);
        }
      }

      // Add a small pause between logs for better readability
      // This creates a more natural typing effect
      setTimeout(() => {
        // Move to the next log
        this.currentLogIndex++;
        this.currentCharIndex = 0;

        // Auto-scroll to the bottom of the logs container
        this.scrollLogsToBottom();

        // Force change detection to update the UI
        this.cdr.detectChanges();
      }, 200); // Increased pause between logs for better readability

      return;
    }

    // Add the next character(s) to the visible content
    // Enhanced character-by-character animation

    // Determine how many characters to add at once based on log type and content
    let charsToAdd = 1; // Default is one character at a time

    if (currentLog.type === 'code') {
      // For code logs, we use a variable typing speed based on content type
      // This creates a more realistic coding effect

      // Get the current character and next few characters
      const currentChar = currentContent[this.currentCharIndex];
      const nextChars = currentContent.substring(this.currentCharIndex, this.currentCharIndex + 5);

      // Type faster through whitespace and common patterns
      if (currentChar === ' ' || currentChar === '\n' || currentChar === '\t') {
        charsToAdd = 1; // Type spaces and newlines at normal speed
      } else if (nextChars.match(/[{}\[\]()<>]/)) {
        charsToAdd = 1; // Type brackets one at a time for emphasis
      } else if (nextChars.match(/[a-zA-Z0-9_]+/)) {
        // Type identifiers (variable names, etc.) faster
        const identifierMatch = currentContent.substring(this.currentCharIndex).match(/^[a-zA-Z0-9_]+/);
        if (identifierMatch && identifierMatch[0].length > 3) {
          // For longer identifiers, type faster
          charsToAdd = Math.min(3, identifierMatch[0].length);
        } else {
          charsToAdd = 1;
        }
      } else if (nextChars.match(/["'`].*?["'`]/)) {
        // Type string literals slightly faster
        charsToAdd = 2;
      } else {
        // Default for code is slightly faster than regular logs
        charsToAdd = 2;
      }
    } else {
      // For regular logs, type 1-2 characters at a time
      // This creates a smoother, more natural typing effect
      charsToAdd = 1;

      // Type faster through spaces and punctuation
      const currentChar = currentContent[this.currentCharIndex];
      if (currentChar === ' ' || currentChar === '.' || currentChar === ',' || currentChar === ':') {
        charsToAdd = 2;
      }
    }

    // Limit chars to add to remaining content length
    charsToAdd = Math.min(charsToAdd, currentContent.length - this.currentCharIndex);

    // Update the visible content with the new character
    this.formattedLogMessages[this.currentLogIndex].visibleContent = currentContent.substring(
      0,
      this.currentCharIndex + charsToAdd
    );

    // Force change detection for the current log using Angular's change detection
    this.cdr.detectChanges();

    // Increment the character index by exactly one character
    this.currentCharIndex += charsToAdd;

    // Auto-scroll to the bottom of the logs container
    this.scrollLogsToBottom();
  }

  /**
   * Scrolls the logs container to the bottom to show the latest logs
   */
  private scrollLogsToBottom(): void {
    setTimeout(() => {
      const logsContainer = document.querySelector('.logs-content');
      if (logsContainer) {
        logsContainer.scrollTop = logsContainer.scrollHeight;
      }
    }, 0);
  }

  /**
   * Adds a log update message to the chat window
   * This method is kept for backward compatibility but is no longer used
   * @param logs Array of log messages
   */
  private addLogUpdateToChatWindow(_logs: string[]): void {
    // We don't want to show logs in the chat window anymore
    // Just make sure the logs tab is enabled
    this.isLogsTabEnabled = true;

    // We no longer process logs in the chat window
  }

  /**
   * Formats the LAYOUT_ANALYZED description to be more user-friendly
   * @param description The progress description containing LAYOUT_ANALYZED
   * @returns A formatted description with better structure
   */
  private formatLayoutAnalyzedDescription(description: string): string {
    if (!description.includes('LAYOUT_ANALYZED')) {
      return description;
    }

    // Extract the file list from the description
    const layoutMatch = description.match(/LAYOUT_ANALYZED\s+(.*?)(?:\n|$)/);

    if (layoutMatch && layoutMatch[1]) {
      const fileList = layoutMatch[1].trim();

      // Split the file list by commas or spaces
      let files: string[] = [];

      // Check if the file list contains commas
      if (fileList.includes(',')) {
        files = fileList.split(',').map(file => file.trim());
      }
      // Check if it's a space-separated list
      else if (fileList.includes(' ')) {
        files = fileList.split(/\s+/).filter(file => file.trim() !== '');
      }
      // If it's a single file
      else {
        files = [fileList];
      }

      // Filter out any empty strings
      files = files.filter(file => file.trim() !== '');

      // Create a formatted file list with bullet points
      const formattedFileList = files
        .map(file => {
          // Add file extension icon based on file type
          let icon = '📄';
          if (file.endsWith('.ts') || file.endsWith('.tsx')) icon = '📘';
          else if (file.endsWith('.js') || file.endsWith('.jsx')) icon = '📙';
          else if (file.endsWith('.css')) icon = '🎨';
          else if (file.endsWith('.html')) icon = '🌐';
          else if (file.endsWith('.json')) icon = '📋';

          return `- ${icon} ${file}`;
        })
        .join('\n');

      // Replace the original file list with the formatted one
      return description.replace(
        /LAYOUT_ANALYZED\s+(.*?)(?:\n|$)/,
        `### Project Structure Analysis\n\nThe following files will be generated:\n\n${formattedFileList}\n\n`
      );
    }

    return description;
  }

  /**
   * Adds a progress description to the chat window with a typewriter effect
   * @param description The progress description to add
   */
  // private addProgressDescriptionToChat(description: string): void {
  //   // Store the progress description for the stepper component
  //   // but don't append it to the AI message text

  //   // Just update the lastProgressDescription property which is bound to the stepper
  //   this.lastProgressDescription = description;

  //   // Make sure we have an AI message with hasSteps=true for the stepper
  //   let stepperMessage = this.lightMessages.find(msg => msg.hasSteps);

  //   // If no stepper message exists and we're polling, create one
  //   if (!stepperMessage && this.isPolling) {
  //     stepperMessage = {
  //       text: 'Processing your request...',
  //       from: 'ai' as 'ai',
  //       theme: 'light' as 'light',
  //       hasSteps: true
  //     };
  //     // this.lightMessages.push(stepperMessage);
  //   }

  //   // Force change detection
  //   this.cdr.detectChanges();

  //   // Auto-scroll the chat window
  //   this.scrollChatToBottom();



  /**
   * Track function for ngFor to improve rendering performance
   * This helps Angular identify which items have changed
   * @param index The index of the log in the array
   * @param log The log item
   * @returns A unique identifier for the log
   */
  // trackByLogIndex(index: number, log: any): string {
  //   // Use a combination of index and timestamp for uniqueness
  //   return `${index}-${log.timestamp}-${log.type}`;
  // }

  /**
   * Format code for display in the logs screen
   * This preserves whitespace, indentation, and line breaks
   * @param code The code to format
   * @returns Formatted code with preserved whitespace
   */
  formatCodeForDisplay(code: string): string {
    if (!code) return '';

    // Replace spaces with non-breaking spaces to preserve indentation
    // Replace < with &lt; and > with &gt; to prevent HTML interpretation
    return code
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/ /g, '&nbsp;')
      .replace(/\n/g, '<br>');
  }

  /**
   * Toggle the expansion state of a code log
   * @param log The log to toggle
   */
  toggleCodeExpansion(log: any): void {
    // Ensure the log has an ID
    if (!log.id) {
      log.id = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    }

    // Check if this log is currently being typed
    const isCurrentlyTyping =
      this.isTypingLog &&
      this.currentLogIndex < this.formattedLogMessages.length &&
      this.formattedLogMessages[this.currentLogIndex].id === log.id;

    // If the log is currently being typed, don't allow collapsing
    if (isCurrentlyTyping && this.expandedCodeLogs.has(log.id)) {
      return;
    }

    // Check if the log has finished typing (visibleContent should match content)
    const hasFinishedTyping =
      log.visibleContent && log.content && log.visibleContent.length === log.content.length;

    // Toggle the expansion state
    if (this.expandedCodeLogs.has(log.id)) {
      // Only allow collapsing if typing is complete
      if (hasFinishedTyping || !isCurrentlyTyping) {
        this.expandedCodeLogs.delete(log.id);
      } else {

      }
    } else {
      // Always allow expanding
      this.expandedCodeLogs.add(log.id);
    }
  }

  /**
   * Check if a code log is expanded
   * @param id The ID of the log to check
   * @returns True if the log is expanded, false otherwise
   */
  isCodeExpanded(id: string): boolean {
    // Default to expanded (true) for all code logs
    return id ? this.expandedCodeLogs.has(id) : true;
  }

  /**
   * Get the appropriate layout key for a page index
   * This distributes layouts across pages when multiple layouts are available
   * @param index The page index
   * @returns The layout key to use for this page
   */
  getLayoutForPageIndex(index: number): string {
    if (!this.layoutData || this.layoutData.length === 0) {
      return '';
    }

    // If we only have one layout, use it for all pages
    if (this.layoutData.length === 1) {
      return this.layoutData[0];
    }

    // Distribute layouts evenly across pages
    return this.layoutData[index % this.layoutData.length];
  }

  /**
   * Extract error message from progress description
   * This is used to display error messages in the error page
   */
  extractErrorMessageFromProgressDescription(): void {
    // Get the latest status response from the polling service
    const statusResponse = this.pollingService.getLastStatusResponse();

    // First check if we have a direct response with progress_description in the details
    if (statusResponse &&
        statusResponse.details &&
        statusResponse.details.progress_description &&
        statusResponse.details.progress_description.trim() !== '') {

      this.errorDescription$.next(statusResponse.details.progress_description);
      this.errorTerminalOutput$.next(this.formatErrorOutput(statusResponse.details));
      return;
    }

    // Fallback to the stored progress description from the polling service
    const progressDescription = this.pollingService.getLastProgressDescription();
    if (progressDescription && progressDescription.trim() !== '') {

      // Try to extract error message from progress description
      try {
        // Check if it's a JSON string
        if (progressDescription.includes('{') && progressDescription.includes('}')) {
          const jsonStartIndex = progressDescription.indexOf('{');
          const jsonEndIndex = progressDescription.lastIndexOf('}') + 1;
          const jsonPart = progressDescription.substring(jsonStartIndex, jsonEndIndex);
          const parsedData = JSON.parse(jsonPart);

          // Extract error message from different possible fields using BehaviorSubjects
          if (parsedData.message) {
            this.errorDescription$.next(parsedData.message);
          } else if (parsedData.progress_description) {
            this.errorDescription$.next(parsedData.progress_description);
          } else if (parsedData.details && parsedData.details.message) {
            this.errorDescription$.next(parsedData.details.message);
          } else if (parsedData.details && parsedData.details.progress_description) {
            this.errorDescription$.next(parsedData.details.progress_description);
          } else if (parsedData.error) {
            this.errorDescription$.next(typeof parsedData.error === 'string'
              ? parsedData.error
              : JSON.stringify(parsedData.error));
          }

          // Format the terminal output for better readability using BehaviorSubject
          this.errorTerminalOutput$.next(this.formatErrorOutput(parsedData));
        } else {
          // Use the progress description as is using BehaviorSubjects
          this.errorDescription$.next(progressDescription);
          this.errorTerminalOutput$.next(progressDescription);
        }
      } catch (e) {
        // this.logger.error('Error parsing progress description:', e);
        this.errorDescription$.next(progressDescription);
        this.errorTerminalOutput$.next(progressDescription);
      }
    }
  }

  /**
   * Format error output for better readability in the terminal
   * @param errorData The error data to format
   * @returns Formatted error output as a string
   */
  formatErrorOutput(errorData: any): string {
    try {
      // Create a formatted error object with highlighted sections
      const formattedError: any = {
        error_type: errorData.status || 'ERROR',
        timestamp: new Date().toISOString(),
        details: {}
      };

      // Extract error message
      if (errorData.message) {
        formattedError.message = errorData.message;
      } else if (errorData.progress_description) {
        formattedError.message = errorData.progress_description;
      }

      // Extract error details
      if (errorData.details) {
        formattedError.details = errorData.details;
      }

      // Extract log data if available
      if (errorData.log) {
        try {
          // Try to parse log as JSON
          if (typeof errorData.log === 'string' &&
              (errorData.log.includes('{') || errorData.log.includes('['))) {
            formattedError.log = JSON.parse(errorData.log);
          } else {
            formattedError.log = errorData.log;
          }
        } catch (e) {
          formattedError.log = errorData.log;
        }
      }

      // Add stack trace if available
      if (errorData.stack) {
        formattedError.stack_trace = errorData.stack;
      }

      // Format as pretty JSON with indentation
      return JSON.stringify(formattedError, null, 2);
    } catch (e) {
      // this.logger.error('Error formatting error output:', e);
      // Fallback to simple JSON stringify
      return JSON.stringify(errorData, null, 2);
    }
  }

  // TrackBy functions for ngFor optimization
  trackByLogIndex(index: number, item: any): string {
    return item.id || index.toString();
  }

  trackByLayoutId(index: number, layout: any): string {
    return layout || index.toString();
  }

  trackByPageIndex(index: number): number {
    return index;
  }

  trackByArtifactId(index: number, artifact: any): string {
    return artifact.id || artifact.name || index.toString();
  }
}
