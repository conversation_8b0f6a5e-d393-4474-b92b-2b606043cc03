import { Injectable, inject, DestroyRef } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { createLogger } from '../../../utils/logger';

// Types for node positioning
export interface NodePosition {
  x: number;
  y: number;
}

export interface NodeDimensions {
  width: number;
  height: number;
}

export interface NodeBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface PositioningConfig {
  gridSpacing: { x: number; y: number };
  columnsPerRow: number;
  defaultNodeSize: NodeDimensions;
  adjacentSpacing: { x: number; y: number };
  minDistanceFromSelected: number;
}

export interface PositioningStrategy {
  type: 'grid' | 'adjacent' | 'fallback';
  description: string;
}

export interface PositionCalculationResult {
  positions: NodePosition[];
  strategy: PositioningStrategy;
  metadata: {
    selectedNodePosition?: NodePosition;
    availableSpace: NodeBounds;
    totalNodes: number;
    newNodesCount: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignNodePositioningService {
  private readonly logger = createLogger('UIDesignNodePositioningService');
  private readonly destroyRef = inject(DestroyRef);

  // Default positioning configuration
  private readonly defaultConfig: PositioningConfig = {
    gridSpacing: { x: 480, y: 780 }, // Mobile node spacing
    columnsPerRow: 2,
    defaultNodeSize: { width: 420, height: 720 }, // Mobile device dimensions
    adjacentSpacing: { x: 500, y: 800 }, // Spacing for adjacent placement
    minDistanceFromSelected: 50 // Minimum distance from selected node
  };

  // Configuration state
  private readonly config$ = new BehaviorSubject<PositioningConfig>(this.defaultConfig);

  // Canvas bounds tracking
  private readonly canvasBounds$ = new BehaviorSubject<NodeBounds>({
    x: -2000,
    y: -2000,
    width: 4000,
    height: 4000
  });

  constructor() {
    this.logger.info('🎯 UI Design Node Positioning Service initialized');
  }

  /**
   * Get current positioning configuration
   */
  getConfig(): PositioningConfig {
    return this.config$.value;
  }

  /**
   * Update positioning configuration
   */
  updateConfig(updates: Partial<PositioningConfig>): void {
    const current = this.config$.value;
    const updated = { ...current, ...updates };
    this.config$.next(updated);
    this.logger.info('⚙️ Positioning configuration updated:', updates);
  }

  /**
   * Calculate positions for initial UI Design generation
   * Uses centered grid layout for clean initial presentation
   */
  calculateInitialGenerationPositions(nodeCount: number): PositionCalculationResult {
    this.logger.info('📐 Calculating initial generation positions:', { nodeCount });

    const config = this.config$.value;
    const positions: NodePosition[] = [];

    // Calculate grid layout centered around origin
    for (let i = 0; i < nodeCount; i++) {
      const position = this.calculateGridPosition(i, nodeCount, config);
      positions.push(position);
    }

    const result: PositionCalculationResult = {
      positions,
      strategy: {
        type: 'grid',
        description: 'Centered grid layout for initial generation'
      },
      metadata: {
        availableSpace: this.canvasBounds$.value,
        totalNodes: nodeCount,
        newNodesCount: nodeCount
      }
    };

    this.logger.info('✅ Initial generation positions calculated:', {
      nodeCount,
      strategy: result.strategy.type,
      firstPosition: positions[0],
      lastPosition: positions[positions.length - 1]
    });

    return result;
  }

  /**
   * Calculate positions for regeneration scenarios
   * Implements intelligent positioning based on selected node and available space
   */
  calculateRegenerationPositions(
    existingNodes: Array<{ id: string; position: NodePosition; dimensions?: NodeDimensions }>,
    selectedNodeId: string,
    newNodesCount: number
  ): PositionCalculationResult {
    this.logger.info('🔄 Calculating regeneration positions:', {
      existingNodesCount: existingNodes.length,
      selectedNodeId,
      newNodesCount
    });

    const selectedNode = existingNodes.find(node => node.id === selectedNodeId);
    if (!selectedNode) {
      this.logger.warn('⚠️ Selected node not found, falling back to grid positioning');
      return this.calculateFallbackPositions(existingNodes, newNodesCount);
    }

    // Try adjacent positioning first
    const adjacentResult = this.tryAdjacentPositioning(existingNodes, selectedNode, newNodesCount);
    if (adjacentResult) {
      return adjacentResult;
    }

    // Fall back to grid positioning
    this.logger.info('📍 Adjacent positioning not feasible, using fallback grid positioning');
    return this.calculateFallbackPositions(existingNodes, newNodesCount);
  }

  /**
   * Calculate grid position for a node at given index
   */
  private calculateGridPosition(nodeIndex: number, totalNodes: number, config: PositioningConfig): NodePosition {
    const row = Math.floor(nodeIndex / config.columnsPerRow);
    const col = nodeIndex % config.columnsPerRow;

    // Center the grid
    const totalCols = Math.min(totalNodes, config.columnsPerRow);
    const gridWidth = totalCols * config.gridSpacing.x;
    const startX = -gridWidth / 2 + config.gridSpacing.x / 2;

    const totalRows = Math.ceil(totalNodes / config.columnsPerRow);
    const gridHeight = totalRows * config.gridSpacing.y;
    const startY = -gridHeight / 2 + config.gridSpacing.y / 2;

    return {
      x: startX + col * config.gridSpacing.x,
      y: startY + row * config.gridSpacing.y
    };
  }

  /**
   * Try to position new nodes adjacent to the selected node
   */
  private tryAdjacentPositioning(
    existingNodes: Array<{ id: string; position: NodePosition; dimensions?: NodeDimensions }>,
    selectedNode: { id: string; position: NodePosition; dimensions?: NodeDimensions },
    newNodesCount: number
  ): PositionCalculationResult | null {
    const config = this.config$.value;
    const positions: NodePosition[] = [];

    // Define potential adjacent positions (right, below, left, above)
    const adjacentOffsets = [
      { x: config.adjacentSpacing.x, y: 0 }, // Right
      { x: 0, y: config.adjacentSpacing.y }, // Below
      { x: -config.adjacentSpacing.x, y: 0 }, // Left
      { x: 0, y: -config.adjacentSpacing.y } // Above
    ];

    let placedNodes = 0;
    let offsetIndex = 0;

    while (placedNodes < newNodesCount && offsetIndex < adjacentOffsets.length) {
      const offset = adjacentOffsets[offsetIndex];
      const basePosition = {
        x: selectedNode.position.x + offset.x,
        y: selectedNode.position.y + offset.y
      };

      // Check if this position and subsequent positions are available
      const availablePositions = this.getAvailableAdjacentPositions(
        basePosition,
        existingNodes,
        newNodesCount - placedNodes,
        config
      );

      if (availablePositions.length > 0) {
        positions.push(...availablePositions);
        placedNodes += availablePositions.length;
        break;
      }

      offsetIndex++;
    }

    // If we couldn't place all nodes adjacently, return null for fallback
    if (placedNodes < newNodesCount) {
      this.logger.info('📍 Could not place all nodes adjacently, trying fallback positioning');
      return null;
    }

    const result: PositionCalculationResult = {
      positions,
      strategy: {
        type: 'adjacent',
        description: `Adjacent placement relative to selected node`
      },
      metadata: {
        selectedNodePosition: selectedNode.position,
        availableSpace: this.canvasBounds$.value,
        totalNodes: existingNodes.length + newNodesCount,
        newNodesCount
      }
    };

    this.logger.info('✅ Adjacent positioning successful:', {
      newNodesCount,
      selectedNodeId: selectedNode.id,
      firstNewPosition: positions[0]
    });

    return result;
  }

  /**
   * Get available positions for adjacent placement
   */
  private getAvailableAdjacentPositions(
    basePosition: NodePosition,
    existingNodes: Array<{ position: NodePosition }>,
    maxNodes: number,
    config: PositioningConfig
  ): NodePosition[] {
    const positions: NodePosition[] = [];
    const canvasBounds = this.canvasBounds$.value;

    for (let i = 0; i < maxNodes; i++) {
      const position = {
        x: basePosition.x + (i * config.adjacentSpacing.x),
        y: basePosition.y
      };

      // Check if position is within canvas bounds
      if (!this.isPositionWithinBounds(position, canvasBounds)) {
        break;
      }

      // Check if position conflicts with existing nodes
      if (this.hasPositionConflict(position, existingNodes, config)) {
        break;
      }

      positions.push(position);
    }

    return positions;
  }

  /**
   * Calculate fallback positions when adjacent positioning fails
   */
  private calculateFallbackPositions(
    existingNodes: Array<{ position: NodePosition }>,
    newNodesCount: number
  ): PositionCalculationResult {
    const config = this.config$.value;
    const totalNodes = existingNodes.length + newNodesCount;
    const positions: NodePosition[] = [];

    // Calculate positions for all nodes (existing + new) and take only the new ones
    for (let i = existingNodes.length; i < totalNodes; i++) {
      const position = this.calculateGridPosition(i, totalNodes, config);
      positions.push(position);
    }

    const result: PositionCalculationResult = {
      positions,
      strategy: {
        type: 'fallback',
        description: 'Grid layout continuation after existing nodes'
      },
      metadata: {
        availableSpace: this.canvasBounds$.value,
        totalNodes,
        newNodesCount
      }
    };

    this.logger.info('✅ Fallback positioning calculated:', {
      newNodesCount,
      strategy: result.strategy.type,
      totalNodes
    });

    return result;
  }

  /**
   * Check if position is within canvas bounds
   */
  private isPositionWithinBounds(position: NodePosition, bounds: NodeBounds): boolean {
    const config = this.config$.value;
    return (
      position.x >= bounds.x &&
      position.y >= bounds.y &&
      position.x + config.defaultNodeSize.width <= bounds.x + bounds.width &&
      position.y + config.defaultNodeSize.height <= bounds.y + bounds.height
    );
  }

  /**
   * Check if position conflicts with existing nodes
   */
  private hasPositionConflict(
    position: NodePosition,
    existingNodes: Array<{ position: NodePosition }>,
    config: PositioningConfig
  ): boolean {
    const nodeSize = config.defaultNodeSize;
    const minDistance = config.minDistanceFromSelected;

    return existingNodes.some(existingNode => {
      const distance = Math.sqrt(
        Math.pow(position.x - existingNode.position.x, 2) +
        Math.pow(position.y - existingNode.position.y, 2)
      );
      return distance < minDistance + Math.max(nodeSize.width, nodeSize.height);
    });
  }

  /**
   * Update canvas bounds for positioning calculations
   */
  updateCanvasBounds(bounds: NodeBounds): void {
    this.canvasBounds$.next(bounds);
    this.logger.info('🖼️ Canvas bounds updated:', bounds);
  }

  /**
   * Get current canvas bounds
   */
  getCanvasBounds(): NodeBounds {
    return this.canvasBounds$.value;
  }

  /**
   * Calculate optimal viewport position to show all nodes
   */
  calculateOptimalViewport(allNodePositions: NodePosition[]): { x: number; y: number; zoom: number } {
    if (allNodePositions.length === 0) {
      return { x: 0, y: 0, zoom: 0.5 };
    }

    const config = this.config$.value;

    // Calculate bounding box of all nodes
    const minX = Math.min(...allNodePositions.map(p => p.x));
    const maxX = Math.max(...allNodePositions.map(p => p.x + config.defaultNodeSize.width));
    const minY = Math.min(...allNodePositions.map(p => p.y));
    const maxY = Math.max(...allNodePositions.map(p => p.y + config.defaultNodeSize.height));

    // Calculate center point
    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;

    // Calculate appropriate zoom level
    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;
    const canvasBounds = this.canvasBounds$.value;

    const zoomX = canvasBounds.width / (contentWidth + 200); // Add padding
    const zoomY = canvasBounds.height / (contentHeight + 200);
    const zoom = Math.min(Math.max(Math.min(zoomX, zoomY), 0.1), 1.0); // Clamp between 0.1 and 1.0

    return {
      x: centerX,
      y: centerY,
      zoom
    };
  }
}
