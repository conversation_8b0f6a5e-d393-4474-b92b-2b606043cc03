import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { SafeHtml } from '@angular/platform-browser';
import { createLogger } from '../../../utils/logger';

export interface UIDesignNodeData {
  title: string;
  displayTitle?: string; // Optional formatted title for display
  htmlContent: SafeHtml;
  rawContent: string;
  width: number;
  height: number;
  isLoading: boolean;
  loadingMessage?: string; // Context-specific loading message
  originalNodeId?: string; // For loading nodes, track which original node they represent
  zIndex?: number; // For z-index management
}

export interface UIDesignNodePosition {
  x: number;
  y: number;
}

export interface UIDesignNode {
  id: string;
  type: 'ui-design';
  data: UIDesignNodeData;
  position: UIDesignNodePosition;
  selected: boolean;
  dragging: boolean;
  visible: boolean;
}

export interface UIDesignPage {
  fileName: string;
  content: string;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignNodeService {
  private readonly logger = createLogger('UIDesignNodeService');

  // Node state management
  private readonly nodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private readonly selectedNodes$ = new BehaviorSubject<string[]>([]);

  // Node configuration - Mobile device proportions
  private readonly defaultNodeSize = { width: 420, height: 720 }; // Mobile aspect ratio (9:16 approximately)
  private readonly gridSpacing = { x: 480, y: 780 }; // Adjusted spacing for mobile nodes
  private readonly columnsPerRow = 2;

  constructor() {
    this.logger.info('UI Design Node Service initialized');
  }

  /**
   * Get nodes observable
   */
  get nodes(): Observable<UIDesignNode[]> {
    return this.nodes$.asObservable();
  }

  /**
   * Get selected nodes observable
   */
  get selectedNodes(): Observable<string[]> {
    return this.selectedNodes$.asObservable();
  }

  /**
   * Get current nodes array
   */
  getNodes(): UIDesignNode[] {
    return this.nodes$.value;
  }

  /**
   * Get selected node IDs
   */
  getSelectedNodeIds(): string[] {
    return this.selectedNodes$.value;
  }

  /**
   * Add a new node
   */
  addNode(node: UIDesignNode): void {
    const currentNodes = this.nodes$.value;
    this.nodes$.next([...currentNodes, node]);
    this.logger.info('Node added:', node.id);
  }

  /**
   * Remove a node by ID
   */
  removeNode(nodeId: string): void {
    const currentNodes = this.nodes$.value;
    const filteredNodes = currentNodes.filter(node => node.id !== nodeId);
    this.nodes$.next(filteredNodes);

    // Remove from selection if selected
    this.deselectNode(nodeId);
    this.logger.info('Node removed:', nodeId);
  }

  /**
   * Update a node
   */
  updateNode(nodeId: string, updates: Partial<UIDesignNode>): void {
    const currentNodes = this.nodes$.value;
    const updatedNodes = currentNodes.map(node =>
      node.id === nodeId ? { ...node, ...updates } : node
    );
    this.nodes$.next(updatedNodes);
  }

  /**
   * Select a node
   */
  selectNode(nodeId: string, multiSelect = false): void {
    const currentSelected = this.selectedNodes$.value;

    if (multiSelect) {
      if (!currentSelected.includes(nodeId)) {
        this.selectedNodes$.next([...currentSelected, nodeId]);
      }
    } else {
      this.selectedNodes$.next([nodeId]);
    }

    // Update node selected state
    this.updateNodeSelectionState();
  }

  /**
   * Deselect a node
   */
  deselectNode(nodeId: string): void {
    const currentSelected = this.selectedNodes$.value;
    const filteredSelected = currentSelected.filter(id => id !== nodeId);
    this.selectedNodes$.next(filteredSelected);
    this.updateNodeSelectionState();
  }

  /**
   * Clear all selections
   */
  clearSelection(): void {
    this.selectedNodes$.next([]);
    this.updateNodeSelectionState();
  }

  /**
   * Update node selection state in nodes array
   */
  private updateNodeSelectionState(): void {
    const selectedIds = this.selectedNodes$.value;
    const currentNodes = this.nodes$.value;

    const updatedNodes = currentNodes.map(node => ({
      ...node,
      selected: selectedIds.includes(node.id)
    }));

    this.nodes$.next(updatedNodes);
  }

  /**
   * Generate unique node ID
   */
  generateNodeId(): string {
    return `ui-design-node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Decode HTML entities to prevent double encoding in iframe
   */
  private decodeHtmlEntities(content: string): string {
    if (!content) return '';

    // Create a temporary DOM element to decode HTML entities
    const textarea = document.createElement('textarea');
    textarea.innerHTML = content;
    let decodedContent = textarea.value;

    // Additional manual decoding for common entities that might not be handled
    decodedContent = decodedContent
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .replace(/&#10;/g, '\n')
      .replace(/&nbsp;/g, ' ');

    return decodedContent;
  }

  /**
   * Ensure HTML content has proper styling for iframe rendering
   */
  private ensureProperHtmlStyling(content: string): string {
    if (!content) return '';

    // Check if content already has a complete HTML structure
    if (content.includes('<!DOCTYPE html>') || content.includes('<html>')) {
      return content;
    }

    // Check if content has basic CSS reset
    const hasBasicStyling = content.includes('margin:') || content.includes('padding:') || content.includes('<style>');

    if (hasBasicStyling) {
      return content;
    }

    // Inject basic CSS reset and styling for proper rendering
    const basicCSS = `
      <style>
        * {
          box-sizing: border-box;
        }
        body {
          margin: 0;
          padding: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.5;
          color: #333;
          background: white;
        }
        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 0.5em;
        }
        p {
          margin-top: 0;
          margin-bottom: 1em;
        }
        img {
          max-width: 100%;
          height: auto;
        }
        button {
          padding: 8px 16px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background: white;
          cursor: pointer;
        }
        button:hover {
          background: #f5f5f5;
        }
      </style>
    `;

    // If content is just a fragment, wrap it in a basic HTML structure
    if (!content.includes('<body>')) {
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${basicCSS}
</head>
<body>
  ${content}
</body>
</html>`;
    }

    // If content has body but no head styling, inject CSS into head or body
    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${basicCSS}`);
    } else if (content.includes('<body>')) {
      return content.replace('<body>', `<body>${basicCSS}`);
    }

    return content;
  }

  /**
   * Calculate optimal node position
   */
  calculateNodePosition(index: number, totalNodes: number): UIDesignNodePosition {
    const row = Math.floor(index / this.columnsPerRow);
    const col = index % this.columnsPerRow;

    // Center the grid
    const totalCols = Math.min(totalNodes, this.columnsPerRow);
    const gridWidth = totalCols * this.gridSpacing.x;
    const startX = -gridWidth / 2 + this.gridSpacing.x / 2;

    const totalRows = Math.ceil(totalNodes / this.columnsPerRow);
    const gridHeight = totalRows * this.gridSpacing.y;
    const startY = -gridHeight / 2 + this.gridSpacing.y / 2;

    return {
      x: startX + col * this.gridSpacing.x,
      y: startY + row * this.gridSpacing.y
    };
  }

  /**
   * Create nodes from UI Design pages
   * 🎨 ENHANCED: Includes comprehensive HTML processing for iframe rendering
   */
  createNodesFromPages(pages: UIDesignPage[], sanitizer: any): UIDesignNode[] {
    return pages.map((page, index) => {
      // 🛡️ CRITICAL: Enhance HTML content with comprehensive processing pipeline
      // const enhancedContent = this.enhanceHTMLWithCSS(page.content);
      // Process HTML content to ensure proper rendering
      const decodedContent = this.decodeHtmlEntities(page.content);
      const processedContent = this.ensureProperHtmlStyling(decodedContent);

      const node: UIDesignNode = {
        id: this.generateNodeId(),
        type: 'ui-design',
        data: {
          title: page.fileName,
          htmlContent: sanitizer.bypassSecurityTrustHtml(processedContent),
          rawContent: processedContent,
          width: this.defaultNodeSize.width,
          height: this.defaultNodeSize.height,
          isLoading: false
        },
        position: this.calculateNodePosition(index, pages.length),
        selected: false,
        dragging: false,
        visible: true
      };

      return node;
    });
  }

  /**
   * Create nodes from UI Design pages with custom positions
   * Enhanced for intelligent positioning using the positioning service
   */
  createNodesFromPagesWithPositions(
    pages: UIDesignPage[],
    sanitizer: any,
    positions: { x: number; y: number }[]
  ): UIDesignNode[] {
    this.logger.info('🎨 Creating nodes with custom positions:', {
      pageCount: pages.length,
      positionCount: positions.length
    });

    return pages.map((page, index) => {
      // Process HTML content to ensure proper rendering
      const decodedContent = this.decodeHtmlEntities(page.content);
      const processedContent = this.ensureProperHtmlStyling(decodedContent);

      // Use provided position or fallback to calculated position
      const nodePosition = positions[index] || this.calculateNodePosition(index, pages.length);

      const node: UIDesignNode = {
        id: this.generateNodeId(),
        type: 'ui-design',
        data: {
          title: page.fileName,
          htmlContent: sanitizer.bypassSecurityTrustHtml(processedContent),
          rawContent: processedContent,
          width: this.defaultNodeSize.width,
          height: this.defaultNodeSize.height,
          isLoading: false
        },
        position: nodePosition,
        selected: false,
        dragging: false,
        visible: true
      };

      this.logger.info(`📍 Node ${index} positioned at:`, nodePosition);
      return node;
    });
  }

  /**
   * Set all nodes at once
   */
  setNodes(nodes: UIDesignNode[]): void {
    this.nodes$.next(nodes);
    this.clearSelection();
    this.logger.info('Nodes set:', nodes.length);
  }

  /**
   * Clear all nodes
   */
  clearAll(): void {
    this.nodes$.next([]);
    this.clearSelection();
    this.logger.info('All nodes cleared');
  }

  /**
   * Get node by ID
   */
  getNodeById(nodeId: string): UIDesignNode | undefined {
    return this.nodes$.value.find(node => node.id === nodeId);
  }

  /**
   * Update node position
   */
  updateNodePosition(nodeId: string, position: UIDesignNodePosition): void {
    this.updateNode(nodeId, { position });
  }

  /**
   * Update node size
   */
  updateNodeSize(nodeId: string, width: number, height: number): void {
    const node = this.getNodeById(nodeId);
    if (node) {
      this.updateNode(nodeId, {
        data: { ...node.data, width, height }
      });
    }
  }

  /**
   * Set node dragging state
   */
  setNodeDragging(nodeId: string, dragging: boolean): void {
    this.updateNode(nodeId, { dragging });
  }

  /**
   * Get content bounds for fit-to-view
   */
  getContentBounds(): { width: number; height: number; minX: number; minY: number; maxX: number; maxY: number } {
    const nodes = this.nodes$.value;

    if (nodes.length === 0) {
      return { width: 0, height: 0, minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    nodes.forEach(node => {
      const nodeMinX = node.position.x;
      const nodeMinY = node.position.y;
      const nodeMaxX = node.position.x + node.data.width;
      const nodeMaxY = node.position.y + node.data.height;

      minX = Math.min(minX, nodeMinX);
      minY = Math.min(minY, nodeMinY);
      maxX = Math.max(maxX, nodeMaxX);
      maxY = Math.max(maxY, nodeMaxY);
    });

    return {
      width: maxX - minX,
      height: maxY - minY,
      minX,
      minY,
      maxX,
      maxY
    };
  }

  /**
   * Clear service state
   */
  clear(): void {
    this.clearAll();
    this.logger.info('UI Design Node Service cleared');
  }

  /**
   * 🎨 Enhance HTML content with comprehensive processing pipeline for iframe rendering
   */
  private enhanceHTMLWithCSS(htmlContent: string): string {
    this.logger.info('🔧 Starting HTML enhancement pipeline for iframe rendering');

    // Step 1: Clean and normalize the HTML content
    const cleanedHTML = this.cleanAndNormalizeHTML(htmlContent);

    // Step 2: Check document structure and enhance accordingly
    const hasDoctype = cleanedHTML.includes('<!DOCTYPE');
    const hasHtmlTag = cleanedHTML.includes('<html');
    const hasHead = cleanedHTML.includes('<head');

    let processedHTML: string;

    if (hasDoctype && hasHtmlTag && hasHead) {
      // HTML is complete, enhance existing structure
      processedHTML = this.enhanceCompleteHTML(cleanedHTML);
    } else {
      // HTML is incomplete, create complete document structure
      processedHTML = this.createCompleteHTMLDocument(cleanedHTML);
    }

    // Step 3: Apply iframe-specific optimizations
    const optimizedHTML = this.applyIframeOptimizations(processedHTML);

    // Step 4: Validate and fix any remaining issues
    const finalHTML = this.validateAndFixHTML(optimizedHTML);

    this.logger.info('✅ HTML enhancement pipeline completed');
    return finalHTML;
  }

  /**
   * 🔧 Step 1: Clean and normalize HTML content
   */
  private cleanAndNormalizeHTML(htmlContent: string): string {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '<html><body><p>No content available</p></body></html>';
    }

    // Remove any leading/trailing whitespace
    let cleaned = htmlContent.trim();

    // Fix common HTML issues
    cleaned = cleaned
      // Fix self-closing tags that should be properly closed
      .replace(/<br\s*\/?>/gi, '<br>')
      .replace(/<hr\s*\/?>/gi, '<hr>')
      .replace(/<img([^>]*)\s*\/?>/gi, '<img$1>')
      // Remove any script tags that might cause issues in iframe
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      // Remove any potential XSS vectors
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');

    return cleaned;
  }

  /**
   * 🔧 Create complete HTML document from any content
   */
  private createCompleteHTMLDocument(htmlContent: string): string {
    // Check if this is just a body fragment
    const isBodyFragment = !htmlContent.includes('<html') && !htmlContent.includes('<head');

    if (isBodyFragment) {
      return this.wrapFragmentInCompleteDocument(htmlContent);
    } else {
      // Try to fix incomplete document
      return this.fixIncompleteDocument(htmlContent);
    }
  }

  /**
   * 🔧 Enhance complete HTML document
   */
  private enhanceCompleteHTML(htmlContent: string): string {
    // Parse the HTML to work with it
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    // Enhance the head section
    this.enhanceHeadSection(doc);

    // Enhance the body section
    this.enhanceBodySection(doc);

    // Return the enhanced HTML
    return doc.documentElement.outerHTML;
  }

  /**
   * 🔧 Enhance head section with CSS frameworks and meta tags
   */
  private enhanceHeadSection(doc: Document): void {
    let head = doc.querySelector('head');

    if (!head) {
      head = doc.createElement('head');
      doc.documentElement.insertBefore(head, doc.body);
    }

    // Add essential meta tags if missing
    if (!doc.querySelector('meta[charset]')) {
      const charsetMeta = doc.createElement('meta');
      charsetMeta.setAttribute('charset', 'UTF-8');
      head.insertBefore(charsetMeta, head.firstChild);
    }

    if (!doc.querySelector('meta[name="viewport"]')) {
      const viewportMeta = doc.createElement('meta');
      viewportMeta.setAttribute('name', 'viewport');
      viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0');
      head.appendChild(viewportMeta);
    }

    // Add CSS frameworks
    this.addCSSFrameworks(head);

    // Add custom iframe styles
    this.addIframeStyles(head);
  }

  /**
   * 🔧 Enhance body section for iframe compatibility
   */
  private enhanceBodySection(doc: Document): void {
    let body = doc.querySelector('body');

    if (!body) {
      body = doc.createElement('body');
      doc.documentElement.appendChild(body);
    }

    // Add iframe-specific classes and attributes
    body.classList.add('iframe-content');

    // Ensure proper styling for iframe context
    if (!body.style.margin) {
      body.style.margin = '0';
    }
    if (!body.style.padding) {
      body.style.padding = '20px';
    }
  }

  /**
   * 🔧 Add CSS frameworks to head element
   */
  private addCSSFrameworks(head: HTMLHeadElement): void {
    const frameworks = [
      {
        href: 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
        id: 'tailwind-css'
      },
      {
        href: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
        id: 'bootstrap-css'
      },
      {
        href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
        id: 'google-fonts'
      },
      {
        href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        id: 'font-awesome'
      }
    ];

    frameworks.forEach(framework => {
      if (!head.querySelector(`link[href="${framework.href}"]`)) {
        const link = head.ownerDocument.createElement('link');
        link.rel = 'stylesheet';
        link.href = framework.href;
        link.id = framework.id;
        head.appendChild(link);
      }
    });
  }

  /**
   * 🔧 Add custom iframe styles
   */
  private addIframeStyles(head: HTMLHeadElement): void {
    const styleId = 'iframe-custom-styles';

    if (!head.querySelector(`#${styleId}`)) {
      const style = head.ownerDocument.createElement('style');
      style.id = styleId;
      style.textContent = `
        /* 🎨 Iframe-specific styles for proper rendering */
        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          line-height: 1.6 !important;
          margin: 0 !important;
          padding: 20px !important;
          background: #f8fafc !important;
          min-height: 100vh;
          box-sizing: border-box;
        }

        * {
          box-sizing: border-box !important;
        }

        .container {
          max-width: 100% !important;
          margin: 0 auto !important;
        }

        /* Ensure images are responsive */
        img {
          max-width: 100%;
          height: auto;
        }

        /* Fix button and form styling */
        button, input, select, textarea {
          font-family: inherit;
        }

        /* Ensure proper text rendering */
        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 0.5rem;
        }

        p {
          margin-bottom: 1rem;
        }

        /* Fix any layout issues */
        .row, .col, [class*="col-"] {
          margin: 0;
        }
      `;
      head.appendChild(style);
    }
  }

  /**
   * 🔧 Wrap HTML fragment in complete document
   */
  private wrapFragmentInCompleteDocument(htmlContent: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>UI Design Preview</title>

  <!-- CSS Frameworks -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      line-height: 1.6 !important;
      margin: 0 !important;
      padding: 20px !important;
      background: #f8fafc !important;
      min-height: 100vh;
    }
    * { box-sizing: border-box !important; }
    .container { max-width: 100% !important; margin: 0 auto !important; }
    img { max-width: 100%; height: auto; }
  </style>
</head>
<body class="iframe-content">
  ${htmlContent}
</body>
</html>`;
  }

  /**
   * 🔧 Fix incomplete HTML document
   */
  private fixIncompleteDocument(htmlContent: string): string {
    let fixed = htmlContent;

    // Add DOCTYPE if missing
    if (!fixed.includes('<!DOCTYPE')) {
      fixed = '<!DOCTYPE html>\n' + fixed;
    }

    // Ensure html tag exists
    if (!fixed.includes('<html')) {
      fixed = fixed.replace('<!DOCTYPE html>', '<!DOCTYPE html>\n<html lang="en">');
      fixed += '\n</html>';
    }

    // Ensure head section exists
    if (!fixed.includes('<head')) {
      fixed = fixed.replace('<html', '<html').replace('>', '>\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>UI Design</title>\n</head>');
    }

    // Ensure body section exists
    if (!fixed.includes('<body')) {
      const headEndIndex = fixed.indexOf('</head>');
      if (headEndIndex !== -1) {
        fixed = fixed.substring(0, headEndIndex + 7) + '\n<body>\n' + fixed.substring(headEndIndex + 7) + '\n</body>';
      }
    }

    return fixed;
  }

  /**
   * 🔧 Apply iframe-specific optimizations
   */
  private applyIframeOptimizations(htmlContent: string): string {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    // Remove any elements that might cause issues in iframe
    const problematicElements = doc.querySelectorAll('script[src*="analytics"], script[src*="gtag"], script[src*="facebook"]');
    problematicElements.forEach(el => el.remove());

    // Ensure all links open in new tab to prevent iframe navigation
    const links = doc.querySelectorAll('a[href]');
    links.forEach(link => {
      link.setAttribute('target', '_blank');
      link.setAttribute('rel', 'noopener noreferrer');
    });

    return doc.documentElement.outerHTML;
  }

  /**
   * 🔧 Validate and fix final HTML
   */
  private validateAndFixHTML(htmlContent: string): string {
    // Final validation and cleanup
    let validated = htmlContent;

    // Ensure proper DOCTYPE
    if (!validated.startsWith('<!DOCTYPE html>')) {
      validated = '<!DOCTYPE html>\n' + validated.replace(/^<!DOCTYPE[^>]*>/i, '');
    }

    // Remove any duplicate meta tags
    validated = validated.replace(/<meta charset="UTF-8">\s*<meta charset="UTF-8">/gi, '<meta charset="UTF-8">');
    validated = validated.replace(/<meta name="viewport"[^>]*>\s*<meta name="viewport"[^>]*>/gi, '<meta name="viewport" content="width=device-width, initial-scale=1.0">');

    // Ensure proper HTML structure
    if (!validated.includes('<html')) {
      validated = validated.replace('<!DOCTYPE html>', '<!DOCTYPE html>\n<html lang="en">') + '\n</html>';
    }

    return validated;
  }
}
